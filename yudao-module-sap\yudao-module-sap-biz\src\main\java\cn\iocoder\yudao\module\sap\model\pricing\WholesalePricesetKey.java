package cn.iocoder.yudao.module.sap.model.pricing;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 批发取价表查询组合键
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class WholesalePricesetKey {
    
    /**
     * 默认渠道编码常量
     */
    public static final String DEFAULT_CHANNEL_CODE = "";
    
    /**
     * 默认E3创建人常量
     */
    public static final String DEFAULT_E3_CREATOR = "";
    
    /**
     * 默认单据类型常量
     */
    public static final String DEFAULT_BILL_TYPE = "";
    
    /**
     * E3渠道编码
     */
    private String channelCode;
    
    /**
     * E3创建人
     */
    private String e3Creator;
    
    /**
     * 单据类型
     */
    private String billType;
    
    /**
     * 创建三维组合键
     * 
     * @param channelCode E3渠道编码
     * @param e3Creator E3创建人
     * @param billType 单据类型
     * @return 三维组合键
     */
    public static WholesalePricesetKey of(String channelCode, String e3Creator, String billType) {
        return new WholesalePricesetKey(channelCode, e3Creator, billType);
    }
    
    @Override
    public String toString() {
        return String.format("WholesalePricesetKey{channelCode='%s', e3Creator='%s', billType='%s'}", 
                channelCode, e3Creator, billType);
    }
} 