package cn.iocoder.yudao.module.sap.dal.mysql.cangqiong;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqMule3channelDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 渠道-多选基础资料表 Mapper
 */
@Mapper
public interface CqMule3channelMapper extends BaseMapperX<CqMule3channelDO> {

    /**
     * 根据主表ID查询渠道多选信息
     *
     * @param id 主表ID
     * @return 渠道多选信息列表
     */
    @Select("SELECT FBasedataId as basedataId, FID as id, FPKID as pkId " +
           "FROM tk_yd_mule3channel WHERE FID = #{id}")
    List<CqMule3channelDO> selectByMainId(@Param("id") Long id);

    /**
     * 根据基础资料ID查询关联的主表ID列表
     *
     * @param basedataId 基础资料ID
     * @return 主表ID列表
     */
    @Select("SELECT FBasedataId as basedataId, FID as id, FPKID as pkId " +
           "FROM tk_yd_mule3channel WHERE FBasedataId = #{basedataId}")
    List<CqMule3channelDO> selectByBasedataId(@Param("basedataId") Long basedataId);

    /**
     * 根据主表ID删除渠道多选信息
     *
     * @param id 主表ID
     * @return 删除行数
     */
    @Select("DELETE FROM tk_yd_mule3channel WHERE FID = #{id}")
    int deleteByMainId(@Param("id") Long id);

    /**
     * 根据基础资料ID列表查询关联的主表ID列表
     *
     * @param basedataIds 基础资料ID列表
     * @return 渠道多选信息列表
     */
    @Select("<script>" +
           "SELECT FBasedataId as basedataId, FID as id, FPKID as pkId " +
           "FROM tk_yd_mule3channel WHERE FBasedataId IN " +
           "<foreach collection='basedataIds' item='basedataId' open='(' separator=',' close=')'>" +
           "#{basedataId}" +
           "</foreach>" +
           "</script>")
    List<CqMule3channelDO> selectByBasedataIds(@Param("basedataIds") List<Long> basedataIds);

    /**
     * 根据主表ID列表批量查询渠道多选信息
     *
     * @param mainIds 主表ID列表
     * @return 渠道多选信息列表
     */
    @Select("<script>" +
           "SELECT FBasedataId as basedataId, FID as id, FPKID as pkId " +
           "FROM tk_yd_mule3channel WHERE FID IN " +
           "<foreach collection='mainIds' item='mainId' open='(' separator=',' close=')'>" +
           "#{mainId}" +
           "</foreach>" +
           "</script>")
    List<CqMule3channelDO> selectByMainIds(@Param("mainIds") List<Long> mainIds);
} 