package cn.iocoder.yudao.module.sap.service.cangqiong;

import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqWholesalePricesetDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqWholesalePricesetLDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqMule3channelDO;
import cn.iocoder.yudao.module.sap.model.pricing.WholesalePricesetKey;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 批发取价表 Service 接口
 */
public interface CqWholesalePricesetService {

    /**
     * 获取批发取价表
     *
     * @param id 批发取价表ID
     * @return 批发取价表
     */
    CqWholesalePricesetDO getWholesalePriceset(Long id);

    /**
     * 根据编码获取批发取价表
     *
     * @param number 编码
     * @return 批发取价表
     */
    CqWholesalePricesetDO getWholesalePricesetByNumber(String number);

    /**
     * 根据状态获取批发取价表列表
     *
     * @param status 数据状态
     * @return 批发取价表列表
     */
    List<CqWholesalePricesetDO> getWholesalePricesetListByStatus(String status);

    /**
     * 根据启用状态获取批发取价表列表
     *
     * @param enable 启用状态
     * @return 批发取价表列表
     */
    List<CqWholesalePricesetDO> getWholesalePricesetListByEnable(String enable);

    /**
     * 根据单据类型获取批发取价表列表
     *
     * @param billType 单据类型
     * @return 批发取价表列表
     */
    List<CqWholesalePricesetDO> getWholesalePricesetListByBillType(String billType);

    /**
     * 根据价格类型获取批发取价表列表
     *
     * @param pricingType 价格类型
     * @return 批发取价表列表
     */
    List<CqWholesalePricesetDO> getWholesalePricesetListByPricingType(String pricingType);

    /**
     * 获取所有批发取价表Map
     *
     * @return ID为key的批发取价表Map
     */
    Map<Long, CqWholesalePricesetDO> getAllWholesalePricesetsMap();

    /**
     * 获取批发取价表的多语言信息
     *
     * @param id 主表ID
     * @return 多语言信息列表
     */
    List<CqWholesalePricesetLDO> getWholesalePricesetLangList(Long id);

    /**
     * 获取批发取价表的渠道多选信息
     *
     * @param id 主表ID
     * @return 渠道多选信息列表
     */
    List<CqMule3channelDO> getWholesalePricesetChannelList(Long id);

    /**
     * 批量构建批发取价表的三维组合键到价格类型映射关系
     * <p>
     * 本方法用于批发取价业务的性能优化，通过预构建映射关系避免实时查询数据库。
     * 支持渠道编码、E3创建人、单据类型的三维组合查询，并处理单据类型的逗号分隔拆分。
     * 
     * @return Map&lt;WholesalePricesetKey, String&gt; 
     *         键：三维组合键（渠道编码 + E3创建人 + 单据类型）
     *         值：对应的价格类型（0:实际金额 1:标准金额 2:分销供货价）
     * 
     * @apiNote 执行步骤概述：
     *          1. 查询所有已审核状态的主表记录ID
     *          2. 批量获取主表详细信息和渠道关联数据  
     *          3. 处理单据类型拆分（支持逗号分隔的多类型）
     *          4. 生成所有可能的三维组合并构建映射关系
     * 
     * @implNote 性能特性：
     *           - 采用批量查询减少数据库交互次数
     *           - 支持大数据量处理，记录详细的耗时统计
     *           - 内存中构建完整映射表，适合频繁查询场景
     *           - 处理重复键检测并记录告警日志
     */
    Map<WholesalePricesetKey, String> buildWholesalePricesetKeyToPricingTypeMapping();
} 