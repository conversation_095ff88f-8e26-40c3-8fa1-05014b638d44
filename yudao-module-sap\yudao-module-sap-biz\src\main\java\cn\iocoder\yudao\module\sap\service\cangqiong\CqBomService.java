package cn.iocoder.yudao.module.sap.service.cangqiong;

import java.util.List;
import java.util.Map;

import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.CqBomWithEntriesDTO;
import cn.iocoder.yudao.module.sap.dal.dataobject.bom.CqBomDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.bom.CqBomEntryDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.bom.CqBomLangDO;
import cn.iocoder.yudao.module.sap.model.bom.BomCustomerKey;

/**
 * 销售BOM Service接口
 */
public interface CqBomService {
    
    /**
     * 根据ID查询BOM主表信息
     *
     * @param id 主表ID
     * @return BOM主表信息
     */
    CqBomDO getCqBom(Long id);

    /**
     * 根据编码查询BOM主表信息
     *
     * @param number 编码
     * @return BOM主表信息
     */
    CqBomDO getCqBomByNumber(String number);

    /**
     * 根据主商品编码查询BOM主表信息
     *
     * @param bomNo 主商品编码
     * @return BOM主表信息
     */
    CqBomDO getCqBomByBomNo(String bomNo);

    /**
     * 根据数据状态查询BOM主表信息列表
     *
     * @param status 数据状态
     * @return BOM主表信息列表
     */
    List<CqBomDO> getCqBomsByStatus(String status);

    /**
     * 根据主表ID查询BOM子表信息列表
     *
     * @param bomId 主表ID
     * @return BOM子表信息列表
     */
    List<CqBomEntryDO> getCqBomEntries(Long bomId);

    /**
     * 根据主表ID查询BOM多语言信息列表
     *
     * @param bomId 主表ID
     * @return BOM多语言信息列表
     */
    List<CqBomLangDO> getCqBomLangs(Long bomId);

    /**
     * 根据主商品编码获取BOM结构信息
     *
     * @param bomNo 主商品编码
     * @return BOM子表信息列表
     */
    List<CqBomEntryDO> getBomStructureByBomNo(String bomNo);

    /**
     * 根据客户ID查询有效的BOM列表
     *
     * @param customerId 客户ID
     * @return 有效的BOM列表
     */
    List<CqBomDO> getActiveBomsByCustomer(Long customerId);

    /**
     * 获取BOM详细信息（包含子表信息）
     *
     * @param bomId 主表ID
     * @return BOM主表信息（包含子表列表）
     */
    CqBomDO getBomDetailWithEntries(Long bomId);

    /**
     * 获取所有BOM结构信息
     *
     * @return 所有BOM结构信息
     */
    List<CqBomDO> getAllBomStructures();

    /**
     * 判断指定主商品编码是否存在
     *
     * @param bomNo 主商品编码
     * @return 是否存在
     */
    boolean isBomNoExists(String bomNo);

    /**
     * 获取所有已审核状态的销售BOM数据结构映射
     * 
     * 该方法返回一个Map结构，其中key为BOM主商品编码（bomNo字段），value为包含对应BOM主表信息和BOM子表信息的组合对象。
     * 
     * 返回值结构：
     * - Map的key：BOM主商品编码（CqBomDO.bomNo字段）
     * - Map的value：CqBomWithEntriesDTO对象，包含：
     *   - bomInfo：BOM主表信息（CqBomDO对象）
     *   - entries：BOM子表信息列表（List<CqBomEntryDO>对象）
     * 
     * 性能考虑：
     * - 使用批量查询策略，减少数据库访问次数
     * - 适合一次性获取所有已审核BOM数据的场景
     * - 对于大量数据，建议考虑分页或缓存机制
     * 
     * @return 已审核BOM数据结构映射，key为BOM主商品编码，value为BOM主表和子表的组合对象
     */
    Map<String, CqBomWithEntriesDTO> getAllAuditedBomStructuresMap();

    /**
     * 获取所有已审核状态的销售BOM数据结构映射（按客户分组）
     * 
     * 该方法返回一个Map结构，其中key为BOM编码与客户ID的组合键，value为包含对应BOM主表信息和BOM子表信息的组合对象。
     * 相比于 getAllAuditedBomStructuresMap() 方法，本方法可以区分相同BOM编码但不同客户的数据。
     * 
     * 返回值结构：
     * - Map的key：BomCustomerKey对象，包含：
     *   - bomNo：BOM主商品编码（CqBomDO.bomNo字段）
     *   - customerId：客户ID（CqBomDO.customerId字段）
     * - Map的value：CqBomWithEntriesDTO对象，包含：
     *   - bomInfo：BOM主表信息（CqBomDO对象）
     *   - entries：BOM子表信息列表（List<CqBomEntryDO>对象）
     * 
     * 使用场景：
     * - 需要按客户区分相同BOM编码的不同配置时
     * - 客户特定的BOM结构查询和分析
     * - 支持多客户场景下的BOM数据管理
     * 
     * 性能考虑：
     * - 使用批量查询策略，减少数据库访问次数
     * - 适合一次性获取所有已审核BOM数据的场景
     * - 对于大量数据，建议考虑分页或缓存机制
     * 
     * @return 已审核BOM数据结构映射，key为BOM编码与客户ID组合键，value为BOM主表和子表的组合对象
     */
    Map<BomCustomerKey, CqBomWithEntriesDTO> getAllAuditedBomStructuresMapByCustomer();
} 