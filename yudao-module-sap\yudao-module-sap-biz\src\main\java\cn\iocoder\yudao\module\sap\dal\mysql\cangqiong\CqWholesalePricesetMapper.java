package cn.iocoder.yudao.module.sap.dal.mysql.cangqiong;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqWholesalePricesetDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 批发取价表-主表 Mapper
 */
@Mapper
public interface CqWholesalePricesetMapper extends BaseMapperX<CqWholesalePricesetDO> {

    /**
     * 根据编码查询批发取价表
     *
     * @param number 编码
     * @return 批发取价表DO
     */
    @Select("SELECT FID as id, fnumber as number, fname as name, fenable as enable, " +
           "fstatus as status, fk_yd_e3creator as e3Creator, fk_yd_mulbilltype as billType, " +
           "fk_yd_pricingtype as pricingType, fk_yd_remark as remark, fmasterid as masterId, " +
           "fcreatorid as creatorId, fcreatetime as createTime, fmodifierid as modifierId, " +
           "fmodifytime as modifyTime " +
           "FROM tk_yd_wholesalepriceset WHERE fnumber = #{number}")
    CqWholesalePricesetDO selectByNumber(@Param("number") String number);

    /**
     * 根据状态查询批发取价表列表
     *
     * @param status 数据状态
     * @return 批发取价表列表
     */
    @Select("SELECT FID as id, fnumber as number, fname as name, fenable as enable, " +
           "fstatus as status, fk_yd_e3creator as e3Creator, fk_yd_mulbilltype as billType, " +
           "fk_yd_pricingtype as pricingType, fk_yd_remark as remark, fmasterid as masterId, " +
           "fcreatorid as creatorId, fcreatetime as createTime, fmodifierid as modifierId, " +
           "fmodifytime as modifyTime " +
           "FROM tk_yd_wholesalepriceset WHERE fstatus = #{status}")
    List<CqWholesalePricesetDO> selectByStatus(@Param("status") String status);

    /**
     * 根据启用状态查询批发取价表列表
     *
     * @param enable 启用状态
     * @return 批发取价表列表
     */
    @Select("SELECT FID as id, fnumber as number, fname as name, fenable as enable, " +
           "fstatus as status, fk_yd_e3creator as e3Creator, fk_yd_mulbilltype as billType, " +
           "fk_yd_pricingtype as pricingType, fk_yd_remark as remark, fmasterid as masterId, " +
           "fcreatorid as creatorId, fcreatetime as createTime, fmodifierid as modifierId, " +
           "fmodifytime as modifyTime " +
           "FROM tk_yd_wholesalepriceset WHERE fenable = #{enable}")
    List<CqWholesalePricesetDO> selectByEnable(@Param("enable") String enable);

    /**
     * 根据单据类型查询批发取价表列表
     *
     * @param billType 单据类型
     * @return 批发取价表列表
     */
    @Select("SELECT FID as id, fnumber as number, fname as name, fenable as enable, " +
           "fstatus as status, fk_yd_e3creator as e3Creator, fk_yd_mulbilltype as billType, " +
           "fk_yd_pricingtype as pricingType, fk_yd_remark as remark, fmasterid as masterId, " +
           "fcreatorid as creatorId, fcreatetime as createTime, fmodifierid as modifierId, " +
           "fmodifytime as modifyTime " +
           "FROM tk_yd_wholesalepriceset WHERE fk_yd_mulbilltype = #{billType}")
    List<CqWholesalePricesetDO> selectByBillType(@Param("billType") String billType);

    /**
     * 根据价格类型查询批发取价表列表
     *
     * @param pricingType 价格类型
     * @return 批发取价表列表
     */
    @Select("SELECT FID as id, fnumber as number, fname as name, fenable as enable, " +
           "fstatus as status, fk_yd_e3creator as e3Creator, fk_yd_mulbilltype as billType, " +
           "fk_yd_pricingtype as pricingType, fk_yd_remark as remark, fmasterid as masterId, " +
           "fcreatorid as creatorId, fcreatetime as createTime, fmodifierid as modifierId, " +
           "fmodifytime as modifyTime " +
           "FROM tk_yd_wholesalepriceset WHERE fk_yd_pricingtype = #{pricingType}")
    List<CqWholesalePricesetDO> selectByPricingType(@Param("pricingType") String pricingType);

    /**
     * 查询所有批发取价表Map
     *
     * @return ID为key的批发取价表Map
     */
    @Select("SELECT FID as id, fnumber as number, fname as name, fenable as enable, " +
           "fstatus as status, fk_yd_e3creator as e3Creator, fk_yd_mulbilltype as billType, " +
           "fk_yd_pricingtype as pricingType, fk_yd_remark as remark, fmasterid as masterId, " +
           "fcreatorid as creatorId, fcreatetime as createTime, fmodifierid as modifierId, " +
           "fmodifytime as modifyTime " +
           "FROM tk_yd_wholesalepriceset")
    List<CqWholesalePricesetDO> selectAll();

    /**
     * 查询所有已审核的主表ID列表
     *
     * @return 已审核的主表ID列表
     */
    @Select("SELECT FID FROM tk_yd_wholesalepriceset WHERE fstatus = 'C'")
    List<Long> selectAuditedIds();
} 