package cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 批发退货单拆分明细 DO
 */
@TableName("tk_yd_wholereturn_spliten")
@KeySequence("tk_yd_wholereturn_spliten_seq") 
@Data 
@ToString(callSuper = true)
public class CqWholesaleReturnSplitEntryDO {

    /**
     * ID
     */
    @TableId("FEntryId")
    private Long entryId;

    /**
     * 批发退货单主表ID
     */
    @TableField("FId")
    private Long billId;

    /**
     * 序号
     */
    @TableField("FSeq")
    private Integer seq;

    /**
     * OMS明细行号
     */
    @TableField("fk_yd_omsrownum")
    private Integer omsRowNum;

    /**
     * OMS商品货号
     */
    @TableField("fk_yd_omsgoodsn")
    private String omsGoodsNo;

    /**
     * 拆分物料ID
     */
    @TableField("fk_yd_splitmaterialid")
    private Long splitMaterialId;

    /**
     * 品牌ID
     */
    @TableField("fk_yd_brandid")
    private Long brandId;

    /**
     * 拆分数量
     */
    @TableField("fk_yd_splitqty")
    private BigDecimal splitQty;

    /**
     * 拆分单价
     */
    @TableField("fk_yd_splitprice")
    private BigDecimal splitPrice;

    /**
     * 价税合计
     */
    @TableField("fk_yd_totaltaxamt")
    private BigDecimal totalTaxAmt;

    /**
     * 是否赠品
     */
    @TableField("fk_yd_isgift")
    private Boolean isGift;

    /**
     * 拆分后库存组织仓库ID
     */
    @TableField("fk_yd_splitinvorgstockid")
    private Long splitInvOrgStockId;

    /**
     * 拆分后销售组织仓库ID
     */
    @TableField("fk_yd_splitsalorgstockid")
    private Long splitSalOrgStockId;
    
    /**
     * 下游单号
     */
    @TableField("fk_yd_downstreambillno")
    private String downstreamBillNo;

    /**
     * 是否剔除物料
     */
    @TableField("fk_yd_excludematsplit")
    private Boolean excludeMatSplit;

    /**
     * 库存组织ID
     */
    @TableField("fk_yd_invorgid")
    private Long invOrgId;

    /**
     * 物料组ID
     */
    @TableField("fk_yd_matgroupid")
    private Long matGroupId;

    /**
     * 产品类型
     */
    @TableField("fk_yd_producttype")
    private String productType; 
    
    /**
     * 未匹配批发取价表
     */
    @TableField("fk_yd_nowholesaleprice")
    private Boolean notMatchWholesalePrice;

    /**
     * 是否缺失分销供货价格
     */
    @TableField("fk_yd_ismissingdistprice")
    private Boolean isMissingDistPrice;
    
} 