package cn.iocoder.yudao.module.sap.service.cangqiong.impl;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqWholesalePricesetDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqWholesalePricesetLDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqMule3channelDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.e3.CqE3ChannelDO;
import cn.iocoder.yudao.module.sap.dal.mysql.cangqiong.CqWholesalePricesetMapper;
import cn.iocoder.yudao.module.sap.dal.mysql.cangqiong.CqWholesalePricesetLMapper;
import cn.iocoder.yudao.module.sap.dal.mysql.cangqiong.CqMule3channelMapper;
import cn.iocoder.yudao.module.sap.dal.mysql.e3.CqE3ChannelMapper;
import cn.iocoder.yudao.module.sap.model.pricing.WholesalePricesetKey;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqWholesalePricesetService;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 批发取价表 Service 实现类
 */
@Service
@Validated
@Slf4j
@DS("cq_scm")
public class CqWholesalePricesetServiceImpl implements CqWholesalePricesetService {

    @Resource
    private CqWholesalePricesetMapper wholesalePricesetMapper;
    
    @Resource
    private CqWholesalePricesetLMapper wholesalePricesetLMapper;
    
    @Resource
    private CqMule3channelMapper mule3channelMapper;
    
    @Resource
    private CqE3ChannelMapper e3ChannelMapper;
 
    @Override
    public CqWholesalePricesetDO getWholesalePriceset(Long id) {
        return wholesalePricesetMapper.selectById(id);
    }

    @Override
    public CqWholesalePricesetDO getWholesalePricesetByNumber(String number) {
        return wholesalePricesetMapper.selectByNumber(number);
    }

    @Override
    public List<CqWholesalePricesetDO> getWholesalePricesetListByStatus(String status) {
        return wholesalePricesetMapper.selectByStatus(status);
    }

    @Override
    public List<CqWholesalePricesetDO> getWholesalePricesetListByEnable(String enable) {
        return wholesalePricesetMapper.selectByEnable(enable);
    }

    @Override
    public List<CqWholesalePricesetDO> getWholesalePricesetListByBillType(String billType) {
        return wholesalePricesetMapper.selectByBillType(billType);
    }

    @Override
    public List<CqWholesalePricesetDO> getWholesalePricesetListByPricingType(String pricingType) {
        return wholesalePricesetMapper.selectByPricingType(pricingType);
    }

    @Override
    public Map<Long, CqWholesalePricesetDO> getAllWholesalePricesetsMap() {
        List<CqWholesalePricesetDO> list = wholesalePricesetMapper.selectAll();
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(CqWholesalePricesetDO::getId, Function.identity()));
    } 

    @Override
    public List<CqWholesalePricesetLDO> getWholesalePricesetLangList(Long id) {
        return wholesalePricesetLMapper.selectByMainId(id);
    }

    @Override
    public List<CqMule3channelDO> getWholesalePricesetChannelList(Long id) {
        return mule3channelMapper.selectByMainId(id);
    }

    /**
     * 渠道映射关系数据结构
     */
    private static class ChannelMappings {
        private final Map<Long, List<String>> mainIdToChannelCodesMap;
        
        public ChannelMappings(Map<Long, List<String>> mainIdToChannelCodesMap) {
            this.mainIdToChannelCodesMap = mainIdToChannelCodesMap != null ? mainIdToChannelCodesMap : new HashMap<>();
        }
        
        public List<String> getChannelCodes(Long mainId) {
            List<String> channelCodes = mainIdToChannelCodesMap.get(mainId);
            return CollUtil.isNotEmpty(channelCodes) ? channelCodes : 
                Collections.singletonList(WholesalePricesetKey.DEFAULT_CHANNEL_CODE);
        }
    }

    @Override
    public Map<WholesalePricesetKey, String> buildWholesalePricesetKeyToPricingTypeMapping() {
        log.info("[buildWholesalePricesetKeyToPricingTypeMapping][开始批量查询所有批发取价表映射关系]");
        
        // 第一步：查询所有已审核的主表ID
        List<Long> auditedMainIds = wholesalePricesetMapper.selectAuditedIds();
        log.info("[buildWholesalePricesetKeyToPricingTypeMapping][查询到 {} 个已审核的主表记录]", auditedMainIds.size());
        
        if (CollUtil.isEmpty(auditedMainIds)) {
            return new HashMap<>();
        }
        
        // 第二步：根据主表ID列表查询所有主表记录
        List<CqWholesalePricesetDO> auditedRecords = wholesalePricesetMapper.selectBatchIds(auditedMainIds);
        log.info("[buildWholesalePricesetKeyToPricingTypeMapping][查询到 {} 个主表详细记录]", auditedRecords.size());
        
        // 第三步：批量查询渠道关联信息
        long startTime = System.currentTimeMillis();
        ChannelMappings channelMappings = buildChannelMappings(auditedMainIds);
        long mappingTime = System.currentTimeMillis() - startTime;
        log.info("[buildWholesalePricesetKeyToPricingTypeMapping][渠道映射构建耗时: {} ms]", mappingTime);
        
        // 第四步：按三维组合键分组并处理单据类型拆分
        Map<WholesalePricesetKey, String> resultMap = new HashMap<>();
        int originalRecordCount = auditedRecords.size();
        int splitRecordCount = 0;
        
        for (CqWholesalePricesetDO record : auditedRecords) {
            // 获取关联的渠道编码列表
            List<String> channelCodes = channelMappings.getChannelCodes(record.getId());
            
            // 拆分单据类型字符串
            List<String> billTypes = splitBillTypes(record.getBillType());
            
            // 生成所有可能的三维组合：渠道编码 × E3创建人 × 单据类型
            for (String channelCode : channelCodes) {
                for (String billType : billTypes) {
                    splitRecordCount++;
                    
                    // 标准化字段值
                    String sanitizedChannelCode = sanitizeChannelCode(channelCode);
                    String sanitizedE3Creator = sanitizeE3Creator(record.getE3Creator());
                    String sanitizedBillType = sanitizeBillType(billType);
                    
                    // 构建三维组合键
                    WholesalePricesetKey key = WholesalePricesetKey.of(
                            sanitizedChannelCode, sanitizedE3Creator, sanitizedBillType);
                    
                    // 检查是否存在重复的组合键
                    if (resultMap.containsKey(key)) {
                        log.warn("[buildWholesalePricesetKeyToPricingTypeMapping][发现重复的三维组合键: 渠道编码:{}, E3创建人:{}, 单据类型:{}]", 
                            sanitizedChannelCode, sanitizedE3Creator, sanitizedBillType);
                    } else {
                        resultMap.put(key, record.getPricingType());
                    }
                }
            }
        }
        
        // 计算总耗时并记录统计信息
        long totalTime = System.currentTimeMillis() - (startTime - mappingTime);
        log.info("[buildWholesalePricesetKeyToPricingTypeMapping][批量查询完成，原始记录: {}, 拆分后记录: {}, 最终组合键: {}, 总耗时: {} ms]", 
            originalRecordCount, splitRecordCount, resultMap.size(), totalTime);
        return resultMap;
    }

    /**
     * 批量构建渠道映射关系
     * 
     * @param mainIds 主表ID列表
     * @return 渠道映射关系
     */
    private ChannelMappings buildChannelMappings(List<Long> mainIds) {
        if (CollUtil.isEmpty(mainIds)) {
            return new ChannelMappings(new HashMap<>());
        }
        
        // 批量查询渠道关联信息
        List<CqMule3channelDO> channelRelations = mule3channelMapper.selectByMainIds(mainIds);
        
        // 收集所有的渠道ID
        List<Long> channelIds = channelRelations.stream()
                .map(CqMule3channelDO::getBasedataId)
                .distinct()
                .collect(Collectors.toList());
        
        // 批量查询E3渠道信息
        List<CqE3ChannelDO> e3Channels = e3ChannelMapper.selectByIds(channelIds);
        Map<Long, String> channelIdToCodeMap = e3Channels.stream()
                .collect(Collectors.toMap(CqE3ChannelDO::getFid, CqE3ChannelDO::getFnumber));
        
        // 构建主表ID到渠道编码列表的映射
        Map<Long, List<String>> mainIdToChannelCodesMap = channelRelations.stream()
                .filter(relation -> channelIdToCodeMap.containsKey(relation.getBasedataId()))
                .collect(Collectors.groupingBy(
                        CqMule3channelDO::getId,
                        Collectors.mapping(
                                relation -> channelIdToCodeMap.get(relation.getBasedataId()),
                                Collectors.toList()
                        )
                ));
        
        log.info("[buildChannelMappings][批量查询完成，渠道关联记录: {}, E3渠道记录: {}, 主表数: {}]", 
            channelRelations.size(), e3Channels.size(), mainIds.size());
        
        return new ChannelMappings(mainIdToChannelCodesMap);
    }

    /**
     * 拆分单据类型字符串
     * 
     * @param billType 多单据类型字符串，可能包含逗号分隔的值
     * @return 单据类型列表
     */
    private List<String> splitBillTypes(String billType) {
        if (billType == null || billType.trim().isEmpty()) {
            return Collections.singletonList(WholesalePricesetKey.DEFAULT_BILL_TYPE);
        }
        
        // 按逗号分割并去除空白字符
        String[] types = billType.split(",");
        List<String> result = new ArrayList<>();
        
        for (String type : types) {
            String trimmed = type.trim();
            if (!trimmed.isEmpty()) {
                result.add(trimmed);
            }
        }
        
        // 如果分割后没有有效的单据类型，返回默认类型
        return result.isEmpty() ? 
            Collections.singletonList(WholesalePricesetKey.DEFAULT_BILL_TYPE) : result;
    }

    /**
     * 标准化渠道编码
     * 
     * @param channelCode 原始渠道编码
     * @return 标准化后的渠道编码
     */
    private String sanitizeChannelCode(String channelCode) {
        if (channelCode == null || channelCode.trim().isEmpty()) {
            return WholesalePricesetKey.DEFAULT_CHANNEL_CODE;
        }
        return channelCode.trim();
    }

    /**
     * 标准化E3创建人
     * 
     * @param e3Creator 原始E3创建人
     * @return 标准化后的E3创建人
     */
    private String sanitizeE3Creator(String e3Creator) {
        if (e3Creator == null || e3Creator.trim().isEmpty()) {
            return WholesalePricesetKey.DEFAULT_E3_CREATOR;
        }
        return e3Creator.trim();
    }

    /**
     * 标准化单据类型
     * 
     * @param billType 原始单据类型
     * @return 标准化后的单据类型
     */
    private String sanitizeBillType(String billType) {
        if (billType == null || billType.trim().isEmpty()) {
            return WholesalePricesetKey.DEFAULT_BILL_TYPE;
        }
        return billType.trim();
    }
} 