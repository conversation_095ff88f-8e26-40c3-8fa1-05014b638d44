package cn.iocoder.yudao.module.sap.model.bom;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * BOM编码与客户ID组合键
 * 用于在Map中索引BOM数据与客户的关联关系
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BomCustomerKey {
    
    /**
     * BOM主商品编码
     */
    private String bomNo;
    
    /**
     * 客户ID
     */
    private Long customerId;
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BomCustomerKey that = (BomCustomerKey) o;
        return Objects.equals(bomNo, that.bomNo) && 
               Objects.equals(customerId, that.customerId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(bomNo, customerId);
    }
    
    @Override
    public String toString() {
        return "BomCustomerKey{" +
                "bomNo='" + bomNo + '\'' +
                ", customerId=" + customerId +
                '}';
    }
} 