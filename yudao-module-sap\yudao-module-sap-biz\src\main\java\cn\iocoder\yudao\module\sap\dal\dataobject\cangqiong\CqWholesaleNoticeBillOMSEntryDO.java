package cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 批发通知单明细表 DO
 *
 * <AUTHOR>
 */
@TableName("tk_yd_wholesalenotbill_en")
@KeySequence("tk_yd_wholesalenotbill_en_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode
@ToString
public class CqWholesaleNoticeBillOMSEntryDO {

    /**
     * 主表ID
     */
    @TableField("FId")
    private Long billId;

    /**
     * 明细表ID
     */
    @TableId(value = "FEntryId")
    private Long entryId;

    /**
     * 分录行号
     */
    @TableField("FSeq")
    private Integer seq;

    /**
     * 修改人
     */
    @TableField("fmodifierfield")
    private Long modifierId;

    /**
     * 修改日期
     */
    @TableField("fmodifydatefield")
    private LocalDateTime modifyDate;

    /**
     * 商品货号
     */
    @TableField("fk_yd_goodsnum")
    private String goodsNum;

    /**
     * 单价
     */
    @TableField("fk_yd_price")
    private BigDecimal price;

    /**
     * 数量
     */
    @TableField("fk_yd_qty")
    private BigDecimal qty;

    /**
     * 实际金额
     */
    @TableField("fk_yd_amount")
    private BigDecimal amount;

    /**
     * 标准金额
     */
    @TableField("fk_yd_stdamount")
    private BigDecimal stdAmount;

    /**
     * 物料编码(旧)
     */
    @TableField("fk_yd_materialno")
    private String materialNo;

    /**
     * 组合方式
     * 枚举:1:组装方式 2:E3映射表方式 3:助记码方式
     */
    @TableField("fk_yd_combinationtype")
    private String combinationType;

    // /**
    //  * 文本字段
    //  */
    // @TableField("fk_yd_textfield")
    // private String textField;

    /**
     * 备注
     */
    @TableField("fk_yd_remark")
    private String remark;

    /**
     * sap行号
     */
    @TableField("fk_yd_sap_rowindex")
    private Long sapRowIndex;

    /**
     * 剔除物料标志
     */
    @TableField("fk_yd_excludematerialoms")
    private Boolean excludeMaterialOms;

    /**
     * 是否组装品标志
     */
    @TableField("fk_yd_isbom")
    private Boolean isBom;

    /**
     * 物料不存在标志
     */
    @TableField("fk_yd_notexistmaterialoms")
    private Boolean notExistMaterialOms;

    /**
     * 单品物料ID
     */
    @TableField("fk_yd_singlematerialid")
    private Long singleMaterialId;

    /**
     * 异常单据标志
     */
    @TableField("fk_yd_iserrorbill")
    private Boolean isErrorBill;

    /**
     * 异常原因
     * 枚举:0:无异常 1:实际金额<0
     */
    @TableField("fk_yd_errorreason")
    private String errorReason;

    /**
     * 物料对应关系重复标志
     */
    @TableField("fk_yd_ismatrelationrepeat")
    private Boolean isMaterialRelationRepeat;
} 