package cn.iocoder.yudao.module.sap.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 批发单据类型枚举
 * 
 * 用于替代批发取价表中的硬编码单据类型值，提供类型安全和语义明确的枚举定义。
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum WholesaleBillTypeEnum {

    /**
     * 批发销货单
     */
    WHOLESALE_SALES("0", "批发销货单"),
    
    /**
     * 批发退货单
     */
    WHOLESALE_RETURN("1", "批发退货单");

    /**
     * 枚举代码
     */
    private final String code;
    
    /**
     * 枚举名称
     */
    private final String name;
    
    /**
     * 根据代码获取枚举
     *
     * @param code 枚举代码
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static WholesaleBillTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (WholesaleBillTypeEnum billType : values()) {
            if (billType.getCode().equals(code)) {
                return billType;
            }
        }
        return null;
    }
    
    /**
     * 根据代码获取名称
     *
     * @param code 枚举代码
     * @return 对应的枚举名称，如果未找到则返回空字符串
     */
    public static String getNameByCode(String code) {
        WholesaleBillTypeEnum billType = getByCode(code);
        return billType != null ? billType.getName() : "";
    }
} 