package cn.iocoder.yudao.module.sap.service.cangqiong.impl;
 
import cn.iocoder.yudao.module.sap.dal.dataobject.material.CqBdMaterialDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.orgcuspricebill.CqOrgCusPriceBillEntryDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.warehouse.CqBdWarehouseDO;
import cn.iocoder.yudao.module.sap.enums.DownstreamBillTypeEnum;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqDeliveryDetailService;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqDirectwarehouseService;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqInnerRelBillService;
import cn.iocoder.yudao.module.sap.service.cangqiong.DeliveryDetailSettleService;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.module.sap.utils.EnhancedCollectionUtils;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.CqDeliveryDetailRespVO;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.CqDeliveryDetailRespVO.CqDeliveryDetailEntryRespVO;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.CqBomWithEntriesDTO;
import cn.iocoder.yudao.module.sap.dal.dataobject.bom.CqBomEntryDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqCkdygxEntryDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqDirectwarehouseDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqShopCostCenterMapDTO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqSourceDeliveryDetailEntryDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqWldygxEntryDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqYdOutwarehousemapEntryDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.customer.CqCustomerDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.customer.CqDBCustomerRelationEntryDO;
import cn.iocoder.yudao.module.sap.model.delivery.NotSaleInfo;
import cn.iocoder.yudao.module.sap.model.delivery.DeliverySettlementContext;
import cn.iocoder.yudao.module.sap.model.delivery.MaterialMatchResult;
import cn.iocoder.yudao.module.sap.model.delivery.OMSDetailSettleContext;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Set;
import java.util.Collections;
import org.apache.commons.collections4.CollectionUtils;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqDBCustomerRelationService;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqPcckService;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqPcwlService;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqWldygxService;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqYdOutwarehousemapService;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqBdMaterialService;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqBdWarehouseService;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqCkdygxService;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.CqDeliveryDetailSettleInfoVO;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqE3ParamSettingService;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqOrgCusPriceBillService;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.DeliveryDateStatDTO;
import cn.iocoder.yudao.module.sap.dal.mysql.cangqiong.CqDeliveryDetailMapper;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.BatchDeliveryDetailEntrySettleUpdateDTO;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.DeliveryDetailEntrySettleUpdateDTO;
import java.util.concurrent.atomic.AtomicLong;
import cn.iocoder.yudao.module.sap.model.delivery.OutwarehouseMapKey;
import cn.iocoder.yudao.module.sap.model.delivery.PlatformMaterialKey;
import cn.iocoder.yudao.module.sap.model.delivery.PlatformWarehouseKey;
import cn.iocoder.yudao.module.sap.model.delivery.PlatformShopKey;

import java.time.LocalDate;
import java.time.LocalDateTime;
import cn.iocoder.yudao.module.sap.model.delivery.InnerRelKey;
import cn.iocoder.yudao.module.sap.model.delivery.InnerRelCombinedInfo;
import cn.iocoder.yudao.module.sap.model.delivery.DirectwarehouseKey;
import cn.iocoder.yudao.module.sap.model.delivery.MainSettleCacheData;
import cn.iocoder.yudao.module.sap.model.delivery.SettleStatistics;
import cn.iocoder.yudao.module.sap.model.delivery.OMSSettleCacheData;
import cn.iocoder.yudao.module.sap.model.delivery.SplitSettleCacheData;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.DeliveryDetailSettleParamsDTO;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.module.sap.utils.DingDingUtils;
import java.time.format.DateTimeFormatter;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqBomService;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqCustomerService;
import cn.iocoder.yudao.module.sap.service.cangqiong.processor.CustomerRelationProcessor;
import cn.iocoder.yudao.module.sap.service.cangqiong.processor.CustomerRelationProcessorFactory;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqShopCostCenterMapService;

@Service
@Slf4j
public class DeliveryDetailSettleServiceImpl implements DeliveryDetailSettleService {

    /**
     * 分层查询阈值：当记录数超过此值时，采用分层查询策略
     */
    private static final int LAYERED_QUERY_THRESHOLD = 5000;

    /**
     * 单日最大记录数阈值：当单日记录数超过此值时，记录警告日志
     */
    private static final int MAX_DAILY_RECORDS_THRESHOLD = 10000;

    /**
     * 总记录数警告阈值：当总记录数超过此值时，记录警告日志  
     */
    private static final int TOTAL_RECORDS_WARNING_THRESHOLD = 50000;

    /**
     * 金额累计追踪器，用于组装品拆分时的精度控制
     */
    private static class AmountAccumulator {
        private BigDecimal totalTaxAmount = BigDecimal.ZERO;
        private BigDecimal avgLogisticsCost = BigDecimal.ZERO;
        private BigDecimal shelvesPrice = BigDecimal.ZERO;
        private BigDecimal totalDiscountAmount = BigDecimal.ZERO;
        private BigDecimal splitShareAmount = BigDecimal.ZERO;

        public BigDecimal getTotalTaxAmount() {
            return totalTaxAmount;
        }

        public void addTotalTaxAmount(BigDecimal amount) {
            this.totalTaxAmount = this.totalTaxAmount.add(amount);
        }

        public BigDecimal getAvgLogisticsCost() {
            return avgLogisticsCost;
        }

        public void addAvgLogisticsCost(BigDecimal amount) {
            this.avgLogisticsCost = this.avgLogisticsCost.add(amount);
        }

        public BigDecimal getShelvesPrice() {
            return shelvesPrice;
        }

        public void addShelvesPrice(BigDecimal amount) {
            this.shelvesPrice = this.shelvesPrice.add(amount);
        }

        public BigDecimal getTotalDiscountAmount() {
            return totalDiscountAmount;
        }

        public void addTotalDiscountAmount(BigDecimal amount) {
            this.totalDiscountAmount = this.totalDiscountAmount.add(amount);
        }

        public BigDecimal getSplitShareAmount() {
            return splitShareAmount;
        }

        public void addSplitShareAmount(BigDecimal amount) {
            this.splitShareAmount = this.splitShareAmount.add(amount);
        }
    }

    @Autowired
    private CqDeliveryDetailService cqDeliveryDetailService;

    @Autowired
    private CqDBCustomerRelationService cqDBCustomerRelationService;

    @Autowired
    private CqPcckService cqPcckService;

    @Autowired
    private CqPcwlService cqPcwlService; 

    @Autowired
    private CqWldygxService cqWldygxService;

    @Autowired
    private CqBdMaterialService cqBdMaterialService;

    @Autowired
    private CqCkdygxService cqCkdygxService;

    @Autowired
    private CqInnerRelBillService cqInnerRelBillService;

    @Autowired
    private CqYdOutwarehousemapService cqYdOutwarehousemapService;

    @Autowired
    private CqDirectwarehouseService cqDirectwarehouseService;

    @Autowired
    private CqE3ParamSettingService cqE3ParamSettingService;

    @Autowired
    private CqOrgCusPriceBillService cqOrgCusPriceBillService;

    @Autowired
    private CqBdWarehouseService cqBdWarehouseService;

    @Autowired
    private CqDeliveryDetailMapper deliveryDetailMapper;

    @Autowired
    private DingDingUtils dingDingUtils;

    @Autowired
    private CqBomService cqBomService;

    @Autowired
    private CqCustomerService cqCustomerService;
    
    @Autowired
    private CustomerRelationProcessorFactory customerRelationProcessorFactory;

    @Autowired
    private CqShopCostCenterMapService cqShopCostCenterMapService;

    /**
     * 处理主单结算
     * 
     * 主要功能：
     * 1. 根据条件查询待结算的发货明细主单
     * 2. 遍历处理每个发货明细的结算逻辑
     * 3. 更新结算状态和相关字段
     * 4. 统计处理结果并记录日志
     *
     * @param params 结算参数DTO，包含单据编号列表、店铺编号列表、开始日期、结束日期
     */
    @Override
    // @Transactional(rollbackFor = Exception.class) // 暂时注释掉事务，避免大批量数据处理时的性能问题
    public void doMainSettle(DeliveryDetailSettleParamsDTO params) {
        // 参数校验和提取
        if (params == null) {
            log.warn("[doMainSettle][参数为空，使用默认参数]");
            params = new DeliveryDetailSettleParamsDTO();
        }
        
        List<String> billNos = params.getBillNos();
        List<String> shopNos = params.getShopNos();
        LocalDate startDate = params.getStartDate();
        LocalDate endDate = params.getEndDate();
        long methodStartTime = System.currentTimeMillis();
        
        // 参数校验和默认值设置
        if (startDate == null) {
            startDate = LocalDate.now().minusDays(31);
        }
        if (endDate == null) {
            endDate = LocalDate.now();
        }
        
        log.info("[doMainSettle][开始主单结算，单据数量: {}, 店铺数量: {}, 日期范围: {} 到 {}]", 
                billNos != null ? billNos.size() : 0, 
                shopNos != null ? shopNos.size() : 0, startDate, endDate);
        
        try {
            // 1. 先统计各日期的记录数
            List<DeliveryDateStatDTO> dateStats = getDeliveryDateStatistics(billNos, "1", shopNos, startDate, endDate);
            
            if (CollUtil.isEmpty(dateStats)) {
                log.info("[doMainSettle][未找到符合条件的记录]");
                return;
            }
            
            // 2. 计算总记录数并记录统计信息
            long totalRecords = dateStats.stream().mapToLong(DeliveryDateStatDTO::getRecordCount).sum();
            log.info("[doMainSettle][统计完成，共 {} 个日期，总记录数: {}]", dateStats.size(), totalRecords);
            
            // 3. 检查总记录数是否超过警告阈值
            if (totalRecords > TOTAL_RECORDS_WARNING_THRESHOLD) {
                log.warn("[doMainSettle][总记录数({})超过警告阈值({}), 请注意内存使用]", 
                        totalRecords, TOTAL_RECORDS_WARNING_THRESHOLD);
            }
            
            // 4. 如果总记录数较少，使用原始查询方式
            if (totalRecords < LAYERED_QUERY_THRESHOLD) {
                log.info("[doMainSettle][记录数较少({})，使用原始查询方式]", totalRecords);
                executeOriginalMainSettle(billNos, shopNos, startDate, endDate);
                return;
            }
            
            // 5. 执行分层查询处理
            executeLayeredMainSettle(dateStats, billNos, shopNos, methodStartTime);
            
        } catch (Exception e) {
            log.error("[doMainSettle][分层查询异常，回退到原始查询]", e);
            executeOriginalMainSettle(billNos, shopNos, startDate, endDate);
        }
    }

    /**
     * 处理单个发货明细的结算
     *
     * @param deliveryDetail 发货明细
     * @param reasons 失败原因列表
     * @param cacheData 主单结算缓存数据，包含排除仓库编码、仓库对应关系、客户关系等缓存信息
     */
    private void processDeliveryDetail(CqDeliveryDetailRespVO deliveryDetail, 
                                     List<String> reasons, 
                                     MainSettleCacheData cacheData) {
        long startTime = System.currentTimeMillis();
        Long deliveryDetailId = deliveryDetail.getId();

        // 初始化结算信息
        DeliverySettlementContext deliverySettlementContext = initSettleInfoData(deliveryDetail, cacheData.getPlatformMergeParam());
        
        // 处理非销售信息
        processNotSaleInfo(deliverySettlementContext, deliveryDetail, cacheData.getShopCostCenterMapDTOs());

        // 如果退货单关联的原始订单不存在，则不进行结算
        if(deliverySettlementContext.getRelatedOrderNotExists() == 1){  
            log.info("[processDeliveryDetail][退货单关联的原始订单不存在，不进行结算，单据编号: {}]", deliveryDetail.getBillNo());
        }else{
            // 处理客户关系 - 传入缓存参数，使用旧逻辑
            processCustomerRelation(deliverySettlementContext, deliveryDetail, cacheData.getCustomerRelationEntriesMap(), cacheData.getCustomerMap(), false);
            
            // 处理仓库信息
            processWarehouseInfo(deliverySettlementContext, deliveryDetail, cacheData.getExcludedWarehouseCodes(), cacheData.getCkdygxEntriesMap(), cacheData.getWarehouseMap());            
        }       

        // 构建结算信息
        CqDeliveryDetailSettleInfoVO settleInfo = buildSettleInfo(deliveryDetailId, deliverySettlementContext);
        
        // 计算结算状态和失败原因
        String[] statusAndReason = calculateSettleStatusAndReasons(settleInfo, reasons);
        String settleStatus = statusAndReason[0];
        String failReason = statusAndReason[1];
        
        // 统一更新结算信息和状态（性能优化：合并两次数据库操作为一次）
        boolean success = cqDeliveryDetailService.updateDeliveryDetailSettleInfoAndStatus(settleInfo, settleStatus, failReason);
        
        long endTime = System.currentTimeMillis();
        if (!success) {
            log.error("[processDeliveryDetail][统一更新结算信息和状态失败，单据编号: {}, ID: {}, 耗时: {}ms]", deliveryDetail.getBillNo(), deliveryDetailId, endTime - startTime);
        } else {
            log.info("[processDeliveryDetail][处理发货明细结算完成，单据编号: {}, ID: {}, 结算状态: {}, 耗时: {}ms]", deliveryDetail.getBillNo(), deliveryDetailId, settleStatus, endTime - startTime);
        }
    }

    /**
     * 初始化结算信息数据（使用构建器模式）
     *
     * @param deliveryDetail 发货明细
     * @param platformMergeParam 平台合并匹配参数
     * @return 结算信息数据对象
     */
    private DeliverySettlementContext initSettleInfoData(CqDeliveryDetailRespVO deliveryDetail, String platformMergeParam) {
        // 使用构建器模式创建结算上下文对象，支持链式调用和参数验证
        return DeliverySettlementContext.builder()
                // 基础状态标识设置
                .isNotExitCustomer(0)        // 客户是否存在(0-存在 1-不存在)
                .isBrandSplitBill(0)         // 是否按品牌分单(0-不分单 1-分单)
                .isExcludedWarehouse(0)      // 是否为排除仓库(0-不排除 1-排除)
                .isNotSendEas(0)             // 是否不发送到EAS(0-传EAS 1-不传EAS)
                .isNotSale(0)                // 是否非销售订单(0-正常销售 1-非销售)
                .relatedOrderNotExists(0)    // 退货单关联的原始订单是否存在(0-存在 1-不存在)
                
                // 平台相关信息设置
                .originalPlatform(deliveryDetail.getPlatform())  // 原始平台类型
                .platform(deliveryDetail.getPlatform())         // 转换后的平台类型（初始设为原始平台）
                
                // 业务单据类型设置
                .downstreamBillType("")      // 下游单据类型（初始为空）
                
                // ID类型字段设置
                .cqWarehouseId(0L)           // 苍穹系统仓库ID
                .cqCustomerId(0L)            // 苍穹系统客户ID
                .shopOrgId(0L)               // 店铺所属组织ID
                
                // 发货明细相关信息设置
                .isReturnOrder(deliveryDetail.getIsReturn())     // 是否为退货订单
                .shopCode(deliveryDetail.getShopNo())            // 店铺编码
                .shopName(deliveryDetail.getShopName())          // 店铺名称
                
                // 非销售信息对象初始化
                .notSaleInfo(new NotSaleInfo())  // 非销售信息对象

                // 积分订单标识设置
                .isPointsOrder(0)
                
                // 库存操作类型初始化
                .ydinvOpType("")                 // 库存操作类型（初始为空字符串）
                
                // 构建最终对象
                .build();
    }

    /**
     * 处理非销售信息
     * 
     * 该方法用于判断和处理非销售订单的相关信息，包括：
     * 1. 根据店铺编码或店铺名称判断是否为非销售订单
     * 2. 处理退货单的关联订单信息获取
     * 3. 解析商家备注中的非销售相关信息
     * 4. 处理积分订单的成本中心映射信息
     *
     * @param data 结算信息数据对象，用于存储处理结果
     * @param deliveryDetail 发货明细对象，包含订单基础信息
     * @param shopCostCenterMapDTOs 店铺成本中心映射配置，key为店铺编码，value为成本中心配置信息
     */
    private void processNotSaleInfo(DeliverySettlementContext data, CqDeliveryDetailRespVO deliveryDetail, Map<String, CqShopCostCenterMapDTO> shopCostCenterMapDTOs) {
        // 检查店铺编码是否包含"temp"关键字，如果包含则标记为非销售单据
        if ((data.getShopCode() != null && data.getShopCode().contains("temp")) 
        || (data.getShopName() != null && data.getShopName().contains("非销"))) {
            // 设置为非销售单据（1表示是非销售单据）
            data.setIsNotSale(1);
            String merchantRemark = deliveryDetail.getMerchantRemark();
            
            // 如果是退货单，需要从关联的原始订单中获取备注信息
            if (data.getIsReturnOrder() != null && "1".equals(data.getIsReturnOrder())) {
                // 根据关联订单号、店铺编号和原始平台查询关联的发货明细
                List<CqDeliveryDetailRespVO> relatedDeliveryDetails = cqDeliveryDetailService
                    .getDeliveryDetailsByOrderInfo(deliveryDetail.getRelatingOrderSn(), deliveryDetail.getShopNo(), data.getOriginalPlatform());
                
                // 如果找到了关联的发货明细，则使用其中的商家备注 和 订单备注
                if (CollectionUtils.isNotEmpty(relatedDeliveryDetails) && relatedDeliveryDetails.size() > 0) {
                    CqDeliveryDetailRespVO cqDeliveryDetailRespVO = relatedDeliveryDetails.get(0);
                    merchantRemark = cqDeliveryDetailRespVO.getMerchantRemark();
                    // 商家备注
                    data.setMerchantRemark(merchantRemark);
                    // 订单备注
                    data.setOrderRemark(cqDeliveryDetailRespVO.getRemark());
                    // 预留单号（昵称）
                    data.setReservedOrderNum(cqDeliveryDetailRespVO.getReservedOrderNum());
                } else {
                    // 如果未找到关联的发货明细，则标记退单关联订单不存在
                    data.setRelatedOrderNotExists(1);
                }
            }
            // 根据退货单设置库存操作类型
            String invOpType = "1".equals(data.getIsReturnOrder()) ? "202" : "201";
            data.getNotSaleInfo().setYdinvOpType(invOpType);
            // 处理卖家备注信息，提取非销售相关信息（如申请部门、预算部门等）
            data.setNotSaleInfo(processSellerMsgOfKeyValue(data.getNotSaleInfo(), merchantRemark));
        }else{
            // 不是非销，判断是否积分订单
            String shopCode = deliveryDetail.getShopNo();
            CqShopCostCenterMapDTO shopCostCenterMapDTO = shopCostCenterMapDTOs.get(shopCode);
            if(shopCostCenterMapDTO != null){
                data.setIsPointsOrder(1);
                // 成本中心
                data.getNotSaleInfo().setYdCostcenter(shopCostCenterMapDTO.getCostCenterNumber());
                // 领料类型（移动原因）
                data.getNotSaleInfo().setYdRequisitionuse(shopCostCenterMapDTO.getMoveReasonNumber());
                // 客户类型
                data.getNotSaleInfo().setYdNotsalecusttype(shopCostCenterMapDTO.getCustomerType());
                // 库存操作类型
                data.getNotSaleInfo().setYdinvOpType(shopCostCenterMapDTO.getDirectInvOpType());
                // 根据是否退货单设置库存操作类型
                String invOpType = "1".equals(data.getIsReturnOrder()) 
                    ? shopCostCenterMapDTO.getBackwardInvOpType()  // 退货单使用逆向操作类型
                    : shopCostCenterMapDTO.getDirectInvOpType();   // 正常单使用正向操作类型
                data.getNotSaleInfo().setYdinvOpType(invOpType);
            }
        }
        
    }

    /**
     * 处理客户关系
     *
     * @param data 结算信息数据
     * @param deliveryDetail 发货明细
     * @param customerRelationEntriesMap 客户关系缓存Map
     * @param customerMap 客户档案缓存Map
     * @param isOldLogic 是否旧逻辑
     */
    private void processCustomerRelation(DeliverySettlementContext data, CqDeliveryDetailRespVO deliveryDetail
        , Map<PlatformShopKey, List<CqDBCustomerRelationEntryDO>> customerRelationEntriesMap
        , Map<String, CqCustomerDO> customerMap
        , boolean isOldLogic) {

        if(isOldLogic){
            // 1. 查询客户关系条目 - 使用缓存
            List<CqDBCustomerRelationEntryDO> customerRelations = queryCustomerRelationsFromCache(data, deliveryDetail, customerRelationEntriesMap);
            if (CollectionUtils.isEmpty(customerRelations)) {
                markCustomerNotExist(data, deliveryDetail);
                return;
            }
            
            // 2. 根据仓库编码过滤匹配的关系
            Optional<CqDBCustomerRelationEntryDO> matchedRelation = filterRelationsByWarehouse(customerRelations, deliveryDetail);
            if (!matchedRelation.isPresent()) {
                markCustomerNotExist(data, deliveryDetail);
                return;
            }
            
            // 3. 设置客户和组织信息
            setupCustomerAndOrgInfo(data, matchedRelation.get());
            
            // 4. 设置下游单据类型和EAS传递标志
            setupDownstreamBillInfo(data, matchedRelation.get());
        }else{
            // 使用策略模式重构后的新逻辑
            // 1. 查询客户关系条目 - 使用缓存
            List<CqDBCustomerRelationEntryDO> customerRelations = queryCustomerRelationsFromCache(data, deliveryDetail, customerRelationEntriesMap);
            if (CollectionUtils.isEmpty(customerRelations)) {
                markCustomerNotExist(data, deliveryDetail);
                return;
            }

            // 2. 根据仓库编码过滤匹配的关系
            Optional<CqDBCustomerRelationEntryDO> matchedRelation = filterRelationsByWarehouse(customerRelations, deliveryDetail);
            
            // 3. 根据业务条件获取对应的处理器并执行处理逻辑
            CustomerRelationProcessor processor = customerRelationProcessorFactory.getProcessor(
                data.getIsNotSale(), 
                matchedRelation.isPresent()
            );
            
            log.debug("[processCustomerRelation][使用处理器: {}, 场景: {}]", 
                processor.getClass().getSimpleName(),
                customerRelationProcessorFactory.getScenarioDescription(data.getIsNotSale(), matchedRelation.isPresent())
            );
            
            processor.process(data, deliveryDetail, customerRelationEntriesMap, customerMap, matchedRelation);
        }

    }

    /**
     * 从缓存中查询客户关系配置（优化版本）
     * 
     * 该方法通过预加载的缓存Map快速查找客户关系配置，避免重复数据库查询，提升性能。
     * 
     * 查询逻辑：
     * 1. 根据业务类型（销售/非销售）确定匹配参数
     * 2. 构建缓存查询键（平台+店铺+匹配类型）
     * 3. 从预加载的缓存Map中获取客户关系配置
     * 4. 返回匹配结果的副本，避免并发修改问题
     * 
     * 匹配规则：
     * - 非销售单据：使用非销售店铺名称进行匹配（匹配类型=2）
     * - 正常销售单据：使用发货明细店铺编码进行匹配（匹配类型=1）
     * 
     * @param data 结算上下文数据，包含平台信息和非销售标识
     * @param deliveryDetail 发货明细数据，包含店铺编码信息
     * @param customerRelationEntriesMap 客户关系缓存Map，以PlatformShopKey为键
     * @return 匹配的客户关系配置列表，如果未找到则返回空列表
     */
    private List<CqDBCustomerRelationEntryDO> queryCustomerRelationsFromCache(DeliverySettlementContext data, CqDeliveryDetailRespVO deliveryDetail, Map<PlatformShopKey, List<CqDBCustomerRelationEntryDO>> customerRelationEntriesMap) {
        // 获取平台信息
        String platform = data.getPlatform();
        String shopCode;
        int matchType;
        
        // if (data.getIsNotSale() == 1) {
        //     // 非销售情况：匹配"平台店铺"使用非销售店铺，匹配类型为店铺名称
        //     // 注意：原本应该使用 data.getNotSaleInfo().getYdNotsaleshop() 获取非销售店铺
        //     // 但为了统一处理，这里改为使用发货明细的店铺名称 deliveryDetail.getShopName()
        //     // shopCode = data.getNotSaleInfo().getYdNotsaleshop();
        //     shopCode = deliveryDetail.getShopName();
        //     matchType = 2; // CqDBCustomerRelationService.MATCH_TYPE_NAME - 按店铺名称匹配
        // } else {
        //     // 正常销售情况：匹配"平台店铺"使用发货明细店铺编码，匹配类型为店铺编码
        //     // 使用发货明细中的店铺编码进行客户关系匹配
        //     shopCode = deliveryDetail.getShopNo();
        //     matchType = 1; // CqDBCustomerRelationService.MATCH_TYPE_CODE - 按店铺编码匹配
        // }

        // 统一逻辑，均使用店铺编码匹配（2025-07-09 by huangzhiqiang）
        shopCode = deliveryDetail.getShopNo();
        matchType = 1; // CqDBCustomerRelationService.MATCH_TYPE_CODE - 按店铺编码匹配
        
        // 从缓存中查找客户关系
        PlatformShopKey cacheKey = new PlatformShopKey(platform, shopCode, matchType);
        List<CqDBCustomerRelationEntryDO> cachedResult = customerRelationEntriesMap.get(cacheKey);
        
        if (cachedResult != null) {
            log.debug("[queryCustomerRelationsFromCache][从缓存命中客户关系，平台: {}, 店铺: {}, 匹配类型: {}, 结果数量: {}]", 
                    platform, shopCode, matchType, cachedResult.size());
            return new ArrayList<>(cachedResult); // 返回副本避免并发修改
        }
        
        log.debug("[queryCustomerRelationsFromCache][缓存未命中客户关系，平台: {}, 店铺: {}, 匹配类型: {}]", 
                platform, shopCode, matchType);
        return Collections.emptyList();
    }
    
    /**
     * 标记客户不存在
     * 
     * @param data 结算上下文数据，用于设置客户不存在标志
     * @param deliveryDetail 发货明细信息，包含单据编号等信息，用于日志记录
     */
    private void markCustomerNotExist(DeliverySettlementContext data, CqDeliveryDetailRespVO deliveryDetail) {
        log.error("[doSettle][单据编号: {} 未找到对应的客户关系配置，标记为客户不存在]", deliveryDetail.getBillNo());
        data.setIsNotExitCustomer(1);
    }

    /**
     * 根据仓库编码过滤客户关系
     * 
     * @param customerRelations 客户关系列表，包含平台店铺与苍穹客户的对应关系
     * @param deliveryDetail 发货明细信息，包含仓库编码等信息
     * @return 匹配的客户关系配置，如果没有匹配则返回空
     */
    private Optional<CqDBCustomerRelationEntryDO> filterRelationsByWarehouse(
            List<CqDBCustomerRelationEntryDO> customerRelations, 
            CqDeliveryDetailRespVO deliveryDetail) {
        
        // 获取发货仓库编码
        String warehouseCode = deliveryDetail.getWarehouse();
        
        // 根据仓库编码过滤有效的客户关系配置（忽略大小写）
        List<CqDBCustomerRelationEntryDO> matchedWarehouseEntries = customerRelations.stream()
            .filter(entry -> entry.getPlatformStockCode().equalsIgnoreCase(warehouseCode))
            .collect(Collectors.toList());
                
        // 如果匹配到多个客户关系配置，记录错误日志
        if (matchedWarehouseEntries.size() > 1) {
            log.error("[doSettle][单据编号: {} 匹配到多个客户关系配置，标记为客户不存在]", deliveryDetail.getBillNo());
            return Optional.empty();
        }

        // 当未匹配到指定仓库的客户关系配置，且存在多个未指定仓库编码的客户关系配置时，
        // 无法确定应使用哪个客户关系，此时标记为客户不存在
        if (matchedWarehouseEntries.isEmpty() && customerRelations.stream()
                .filter(entry -> StrUtil.isEmpty(entry.getPlatformStockCode()))
                .count() > 1) {
            log.error("[doSettle][单据编号: {} 未匹配到仓库且存在多个客户关系配置，标记为客户不存在]", deliveryDetail.getBillNo());
            return Optional.empty();
        }

        // 如果没有匹配仓库的客户关系配置，则使用PlatformStockCode为空的第一条客户关系配置
        CqDBCustomerRelationEntryDO selectedRelation = matchedWarehouseEntries.isEmpty() ? 
            customerRelations.stream()
                .filter(entry -> StrUtil.isEmpty(entry.getPlatformStockCode()))
                .findFirst()
                .orElse(customerRelations.get(0)) : matchedWarehouseEntries.get(0);
        
        return Optional.of(selectedRelation);
    }

    /**
     * 设置客户和组织信息
     * 
     * @param data 结算上下文数据对象，包含结算相关的各种信息
     * @param customerRelation 客户关系配置对象，包含客户ID、对应组织ID和是否按品牌分单等信息
     */
    private void setupCustomerAndOrgInfo(DeliverySettlementContext data, CqDBCustomerRelationEntryDO customerRelation) {
        // 设置苍穹客户ID
        data.setCqCustomerId(customerRelation.getKingdeeCustomerId());
        
        // 设置对应组织ID
        data.setShopOrgId(customerRelation.getCorrespondingOrgId());
        
        // 设置是否按品牌分单
        Integer isBrandSplit = customerRelation.getIsBrandSeparated();
        if (isBrandSplit != null && isBrandSplit == 1) {
            data.setIsBrandSplitBill(1);
        }
    }

    /**
     * 设置下游单据类型和EAS传递标志
     * 
     * @param data 结算上下文数据对象，包含结算相关的各种信息
     * @param customerRelation 客户关系配置对象，包含汇总类型等配置信息
     */
    private void setupDownstreamBillInfo(DeliverySettlementContext data, CqDBCustomerRelationEntryDO customerRelation) {
        // 获取汇总类型
        String sumType = customerRelation.getSummaryType();
        
        // 使用新枚举类根据汇总类型和是否非销确定下游单据类型
        data.setDownstreamBillType(DownstreamBillTypeEnum.getDownstreamBillType(sumType, data.getIsNotSale()));
        
        // 如果下游单据类型为空，则不传EAS
        if (data.getDownstreamBillType() != null && data.getDownstreamBillType().isEmpty()) {
            data.setIsNotSendEas(1);
        }
    }

    /**
     * 处理仓库信息
     * 根据发货明细中的仓库编码，判断是否为排除仓库，并获取对应的苍穹仓库ID和库存组织ID
     *
     * @param data 结算信息数据对象，用于存储处理结果
     * @param deliveryDetail 发货明细对象，包含仓库编码等信息
     * @param excludedWarehouseCodes 排除仓库编码集合，用于判断仓库是否需要排除
     * @param ckdygxEntriesMap 仓库对应关系映射表，key为平台和仓库编码的组合，value为对应关系列表
     * @param warehouseMap 仓库信息映射表，key为仓库ID，value为仓库详细信息
     */
    private void processWarehouseInfo(DeliverySettlementContext data, CqDeliveryDetailRespVO deliveryDetail, Set<String> excludedWarehouseCodes, Map<PlatformWarehouseKey, List<CqCkdygxEntryDO>> ckdygxEntriesMap, Map<Long, CqBdWarehouseDO> warehouseMap) {
        // 若"剔除仓库"为否，将 仓库编码 匹配 {仓库对应关系}表 "业务平台对应仓库"，若不存在，则为是
        // 是否存在排除仓库
        boolean isExcludedWarehouse = excludedWarehouseCodes.contains(deliveryDetail.getWarehouse());
        // 如果仓库在排除列表中，则标记为排除仓库
        if (isExcludedWarehouse) {
            data.setIsExcludedWarehouse(1);
        } else {
            // 如果仓库不在排除列表中，则根据平台和仓库编码查询仓库对应关系
            List<CqCkdygxEntryDO> ckdygxEntryDOs = ckdygxEntriesMap.get(new PlatformWarehouseKey(data.getPlatform(), deliveryDetail.getWarehouse()));
            // 如果找到对应关系，则获取苍穹系统中的仓库ID
            if(!CollectionUtils.isEmpty(ckdygxEntryDOs) && ckdygxEntryDOs.size() == 1) {
                // data.setCqWarehouseId(ckdygxEntryDOs.get(0).getWarehouseId());
                // // 获取仓库的创建组织作为库存组织
                // CqBdWarehouseDO cqBdWarehouseDO = warehouseMap.get(ckdygxEntryDOs.get(0).getWarehouseId());
                // if (cqBdWarehouseDO != null) {
                //     data.setInvOrgId(cqBdWarehouseDO.getCreateOrgId());
                // } else {
                //     log.warn("[processWarehouseInfo][仓库ID: {} 在缓存中未找到]", ckdygxEntryDOs.get(0).getWarehouseId());
                //     data.setInvOrgId(0L);
                // }
            }
        }
    }

    /**
     * 构建结算信息VO对象
     *
     * @param deliveryDetailId 发货明细ID
     * @param data 结算信息数据
     * @return 结算信息VO对象
     */
    private CqDeliveryDetailSettleInfoVO buildSettleInfo(Long deliveryDetailId, DeliverySettlementContext data) {
        // 构建并设置结算信息VO
        CqDeliveryDetailSettleInfoVO settleInfo = new CqDeliveryDetailSettleInfoVO();
        // 设置发货明细ID
        settleInfo.setId(deliveryDetailId);
        // 设置客户是否不存在标志(1:不存在 0:存在)
        settleInfo.setIsNotExitCustomer(data.getIsNotExitCustomer());
        // 设置是否按品牌分单标志(1:是 0:否)
        settleInfo.setIsBrandSplitBill(data.getIsBrandSplitBill());
        // 设置是否排除仓库标志(1:是 0:否)
        settleInfo.setIsExcludedWarehouse(data.getIsExcludedWarehouse());
        // 设置下游单据类型
        settleInfo.setDownstreamBillType(data.getDownstreamBillType());
        // 设置是否不发送到EAS标志(1:不发送 0:发送)
        settleInfo.setIsNotSendEas(data.getIsNotSendEas());
        // 设置苍穹客户ID
        settleInfo.setCqCustomerId(data.getCqCustomerId());
        // 设置是否非销售标志(1:是非销售 0:是销售)
        settleInfo.setIsNotSale(data.getIsNotSale());
        
        // 设置是否积分订单标志(1:是积分订单 0:不是积分订单)
        settleInfo.setIsPointsOrder(data.getIsPointsOrder());

        // 设置退单关联订单是否不存在标志(1:不存在 0:存在)
        settleInfo.setRelatedOrderNotExists(data.getRelatedOrderNotExists());

        // 设置非销售信息字段
        NotSaleInfo notSaleInfo = data.getNotSaleInfo();
        if (notSaleInfo != null) {
            // 设置非销售店铺 - 非销售业务关联的店铺信息
            settleInfo.setYdNotsaleshop(notSaleInfo.getYdNotsaleshop());
            // 设置申请部门 - 非销售时的申请部门信息
            settleInfo.setYdApplydepart(notSaleInfo.getYdApplydepart());
            // 设置领料用途 - 非销售时的领料用途信息
            settleInfo.setYdRequisitionuse(notSaleInfo.getYdRequisitionuse());
            
            // ========== 新增字段映射 - processSellerMsgOfKeyValue方法解析的字段 ==========
            // 设置成本中心 - 从processSellerMsgOfKeyValue方法解析出的成本中心信息
            settleInfo.setCostCenter(notSaleInfo.getYdCostcenter());
            // 设置基金科目 - 从processSellerMsgOfKeyValue方法解析出的基金科目信息
            settleInfo.setFundAccount(notSaleInfo.getYdFundaccount());
            // 设置客户(非销) - 从processSellerMsgOfKeyValue方法解析出的客户信息
            settleInfo.setNotSaleCust(notSaleInfo.getYdNotsalecust());
            // 设置客户类型(非销) - 从processSellerMsgOfKeyValue方法解析出的客户类型信息
            settleInfo.setNotSaleCustType(notSaleInfo.getYdNotsalecusttype());
            
            // 设置库存操作类型 - 从积分订单处理逻辑中获取的库存操作类型
            settleInfo.setYdinvOpType(notSaleInfo.getYdinvOpType());
            
            if(data.getIsReturnOrder() != null && "1".equals(data.getIsReturnOrder())) {
                // 设置商家备注 - 非销售时的商家备注信息
                settleInfo.setMerchantRemark(data.getMerchantRemark());
                // 设置订单备注
                settleInfo.setOrderRemark(data.getOrderRemark());
            }
        }
        // 设置店铺组织ID - 用于关联店铺所属的组织架构
        settleInfo.setShopOrgId(data.getShopOrgId());
        
        // 设置预留单号（昵称） - 用于退货单关联原始订单的预留单号信息
        settleInfo.setReservedOrderNum(data.getReservedOrderNum());
        
        return settleInfo;
    }

    /**
     * 计算结算状态和失败原因
     * 从原updateSettleStatus方法中提取的状态判断逻辑
     *
     * @param settleInfo 结算信息
     * @param reasons 失败原因列表
     * @return 包含状态和失败原因的数组，[0]为状态，[1]为失败原因
     */
    private String[] calculateSettleStatusAndReasons(CqDeliveryDetailSettleInfoVO settleInfo, List<String> reasons) {
        // 如果存在客户不存在、排除仓库、不传EAS的情况，则跳过结算
        if (settleInfo.getIsNotExitCustomer() == 1
                || settleInfo.getIsExcludedWarehouse() == 1
                || settleInfo.getIsNotSendEas() == 1
                || settleInfo.getRelatedOrderNotExists() == 1) {
            if (settleInfo.getIsNotExitCustomer() == 1) {
                reasons.add("客户不存在");
            }
            if (settleInfo.getIsExcludedWarehouse() == 1) {
                reasons.add("排除仓库");
            }
            if (settleInfo.getIsNotSendEas() == 1) {
                reasons.add("不传EAS");
            }
            if (settleInfo.getRelatedOrderNotExists() == 1) {
                reasons.add("退单关联订单不存在");
            }
            return new String[]{"1", String.join("、", reasons)};
        } else {
            // 更新结算状态为"已结算(2)" - 标记该单据已完成结算处理
            return new String[]{"2", String.join("、", reasons)};
        }
    }

    /**
     * 处理键值对格式的商家备注信息解析
     * 
     * 该方法专门用于解析键值对格式的商家备注信息，支持以下格式：
     * $非销店铺=取发货明细E3店铺名称$申请部门=取单头申请部门$品牌=取单头品牌$渠道=取单头渠道$客户=取客户$领料用途=取单头领料用途$成本中心=取成本中心编码$基金科目=取基金科目$客户类型=取客户类型
     * 
     * 解析规则：
     * - 使用$符号作为主分隔符
     * - 使用=符号作为键值分隔符
     * - 支持键值对顺序变化
     * - 单个字段解析失败不影响其他字段
     * 
     * 字段映射关系：
     * - "非销店铺" → ydNotsaleshop
     * - "申请部门" → ydApplydepart
     * - "领料用途" → ydRequisitionuse
     * - "成本中心" → ydCostcenter
     * - "基金科目" → ydFundaccount
     * - "客户" → ydNotsalecust
     * - "客户类型" → ydNotsalecusttype
     *
     * @param notSaleInfo 非销售信息对象，用于存储解析结果
     * @param sellerMsg 商家备注信息，包含用$和=分隔的键值对信息
     * @return 解析成功返回填充后的notSaleInfo对象，解析失败或信息为空返回原对象
     */
    private NotSaleInfo processSellerMsgOfKeyValue(NotSaleInfo notSaleInfo, String sellerMsg) {
        if (StrUtil.isBlank(sellerMsg)) {
            return notSaleInfo;
        }

        log.info("[processSellerMsgOfKeyValue][开始解析键值对格式商家备注信息: {}]", sellerMsg);
        
        try {
            // 使用$分割各个部分
            String[] parts = sellerMsg.split("\\$");

            for (String part : parts) {
                // 跳过空字符串
                if (StrUtil.isBlank(part)) {
                    continue;
                }
                
                // 使用=分割键值对，限制分割次数为2，避免值中包含=时被错误分割
                String[] keyValue = part.split("=", 2);
                if (keyValue.length == 2) {
                    String key = keyValue[0].trim();
                    String value = keyValue[1].trim();

                    // 根据键名设置对应的NotSaleInfo属性值
                    switch (key) {
                        case "非销店铺":
                            notSaleInfo.setYdNotsaleshop(value);
                            break;
                        case "申请部门":
                            notSaleInfo.setYdApplydepart(value);
                            break;
                        case "领料用途":
                            notSaleInfo.setYdRequisitionuse(value);
                            break;
                        case "成本中心":
                            notSaleInfo.setYdCostcenter(value);
                            break;
                        case "基金科目":
                            notSaleInfo.setYdFundaccount(value);
                            break;
                        case "客户":
                            notSaleInfo.setYdNotsalecust(value);
                            break;
                        case "客户类型":
                            notSaleInfo.setYdNotsalecusttype(value);
                            break;
                        default:
                            // 未知键名，记录调试日志但不影响处理
                            log.debug("[processSellerMsgOfKeyValue][发现未知键名: {}, 值: {}, 跳过处理]", key, value);
                            break;
                    }
                } else {
                    // 格式不正确的键值对，记录警告日志
                    log.warn("[processSellerMsgOfKeyValue][键值对格式不正确，跳过: {}]", part);
                }
            }
            
            log.info("[processSellerMsgOfKeyValue][键值对格式商家备注信息解析成功]");
        } catch (Exception e) {
            log.error("[processSellerMsgOfKeyValue][解析键值对格式商家备注信息失败: {}]", e.getMessage(), e);
        }
        
        return notSaleInfo;
    }

    /**
     * 处理OMS发货明细结算
     * 
     * 主要功能：
     * 1. 根据条件查询待结算的发货明细
     * 2. 批量预加载相关基础数据（排除物料、物料组合、物料对应关系等）
     * 3. 遍历处理每个发货明细的结算逻辑
     * 4. 批量更新结算状态和相关字段
     *
     * @param params 结算参数DTO，包含以下字段：
     *               - billNos: 单据编号列表，可为空表示不按单据编号过滤
     *               - shopNos: 店铺编号列表，可为空表示不按店铺过滤
     *               - startDate: 开始日期，可为空表示不按日期过滤
     *               - endDate: 结束日期，可为空表示不按日期过滤
     */
    @Override
    public void doOMSDetailSettle(DeliveryDetailSettleParamsDTO params) {
        // 参数校验和提取
        if (params == null) {
            log.warn("[doOMSDetailSettle][参数为空，使用默认参数]");
            params = new DeliveryDetailSettleParamsDTO();
        }
        
        List<String> billNos = params.getBillNos();
        List<String> shopNos = params.getShopNos();
        LocalDate startDate = params.getStartDate();
        LocalDate endDate = params.getEndDate();
        long methodStartTime = System.currentTimeMillis();
        
        // 参数校验和默认值设置
        if (startDate == null) {
            startDate = LocalDate.now().minusDays(31);
        }
        if (endDate == null) {
            endDate = LocalDate.now();
        }
        
        log.info("[doOMSDetailSettle][开始OMS明细结算，单据数量: {}, 店铺数量: {}, 日期范围: {} 到 {}]", 
                billNos != null ? billNos.size() : 0, 
                shopNos != null ? shopNos.size() : 0, startDate, endDate);
        
        try {
            // 1. 先统计各日期的记录数
            List<DeliveryDateStatDTO> dateStats = getDeliveryDateStatistics(billNos, "2", shopNos, startDate, endDate);
            
            if (CollUtil.isEmpty(dateStats)) {
                log.info("[doOMSDetailSettle][未找到符合条件的记录]");
                return;
            }
            
            // 2. 计算总记录数并记录统计信息
            long totalRecords = dateStats.stream().mapToLong(DeliveryDateStatDTO::getRecordCount).sum();
            log.info("[doOMSDetailSettle][统计完成，共 {} 个日期，总记录数: {}]", dateStats.size(), totalRecords);
            
            // 3. 检查总记录数是否超过警告阈值
            if (totalRecords > TOTAL_RECORDS_WARNING_THRESHOLD) {
                log.warn("[doOMSDetailSettle][总记录数({})超过警告阈值({}), 请注意内存使用]", 
                        totalRecords, TOTAL_RECORDS_WARNING_THRESHOLD);
            }
            
            // 4. 如果总记录数较少，使用原始查询方式
            if (totalRecords < LAYERED_QUERY_THRESHOLD) {
                log.info("[doOMSDetailSettle][记录数较少({})，使用原始查询方式]", totalRecords);
                executeOriginalOMSDetailSettle(billNos, shopNos, startDate, endDate);
                return;
            }
            
            // 5. 执行分层查询处理
            executeLayeredOMSDetailSettle(dateStats, billNos, shopNos, methodStartTime);
            
        } catch (Exception e) {
            log.error("[doOMSDetailSettle][分层查询异常，回退到原始查询]", e);
            executeOriginalOMSDetailSettle(billNos, shopNos, startDate, endDate);
        }
    }

    /**
     * 处理单个发货明细的OMS结算
     * 
     * @param deliveryDetail 发货明细
     * @param cacheData OMS缓存数据
     */
    private void processDeliveryDetailOMS(CqDeliveryDetailRespVO deliveryDetail, 
                                         OMSSettleCacheData cacheData) {
        // 创建批量更新DTO
        BatchDeliveryDetailEntrySettleUpdateDTO batchUpdateDTO = new BatchDeliveryDetailEntrySettleUpdateDTO();
        batchUpdateDTO.setBillNo(deliveryDetail.getBillNo());
        
        // 获取明细列表
        List<CqDeliveryDetailEntryRespVO> entries = deliveryDetail.getEntries();
        if (CollectionUtils.isEmpty(entries)) {
            log.error("[processDeliveryDetailOMS][单据编号: {} 没有可结算的发货明细分录]", deliveryDetail.getBillNo());
            return;
        }
        
        // 创建明细更新列表
        List<DeliveryDetailEntrySettleUpdateDTO> entryUpdateDTOs = new ArrayList<>(entries.size());
        
        // 设置状态标识
        // 标记是否所有物料都被剔除，初始值为true，如果发现任何一个非剔除物料则会设为false
        boolean allMaterialsExcluded = true;
        // 标记是否存在需要处理的物料问题（如物料不存在或关系重复），初始值为false，如果发现任何问题则会设为true
        boolean hasMaterialIssueNeedHandle = false;
        
        // 处理每个明细
        for (CqDeliveryDetailEntryRespVO entry : entries) {
            // 创建明细更新DTO
            DeliveryDetailEntrySettleUpdateDTO entryUpdateDTO = new DeliveryDetailEntrySettleUpdateDTO();
            entryUpdateDTO.setEntryId(entry.getEntryId());
            entryUpdateDTO.setMainId(deliveryDetail.getId());
            
            // 处理物料信息
            String materialNo = entry.getMaterialNo();
            
            // 使用批量查询结果检查是否剔除物料，提升性能
            boolean isExcludedMaterial = cacheData.getExcludedMaterials().contains(materialNo);
            entryUpdateDTO.setIsExcludedMaterial(isExcludedMaterial);
            
            if (!isExcludedMaterial) {
                // 非剔除物料，至少有一个不是剔除物料
                allMaterialsExcluded = false;
                
                // 检查是否组装品 - 使用BOM结构缓存
                CqBomWithEntriesDTO bomWithEntriesDTO = cacheData.getBomStructuresMap().getOrDefault(materialNo, null);
                
                if (bomWithEntriesDTO == null) {
                    // 非组套，调用物料匹配方法
                    String platform = deliveryDetail.getPlatform();
                    // platform = convertPlatformWithParam(platform, cacheData.getPlatformMergeParam());
                    
                    // 调用物料匹配方法，根据平台和物料编号获取匹配结果
                    MaterialMatchResult matchResult = matchOMSDetailMaterial(platform, materialNo, cacheData.getWldygxEntriesMap(), cacheData.getAllApprovedMaterials());
                    // 设置单品物料ID，用于后续结算处理
                    entryUpdateDTO.setSingleMaterialId(matchResult.getMaterialId());
                    // 设置物料是否不存在标志，1表示不存在，0表示存在
                    entryUpdateDTO.setIsNotExitMaterial(matchResult.getIsNotExitMaterial() == 1);
                    // 设置物料关系是否重复标志，1表示重复，0表示不重复
                    entryUpdateDTO.setIsMaterialRelationRepeat(matchResult.getIsMaterialRelationRepeat() == 1);
                    
                    // 如果物料不存在或关系重复且非剔除，标记有问题需处理
                    if ((matchResult.getIsNotExitMaterial() == 1 || matchResult.getIsMaterialRelationRepeat() == 1)) {
                        hasMaterialIssueNeedHandle = true;
                    }
                } else {
                    // 组套
                    entryUpdateDTO.setIsMainMatNumCombined(true);
                    // 设置单品物料ID（为组套，设置单品物料ID为0）
                    entryUpdateDTO.setSingleMaterialId(0L);
                    
                    // // 多级组套处理：当一个物料编号对应多个组套配置时，表示存在多级组套关系
                    // // 这种情况下需要标记为物料关系重复，因为系统无法确定应该使用哪个组套配置
                    // if (combinations.size() > 1) {
                    //     // 设置物料关系重复标志为true，表示该物料存在多个组套配置
                    //     entryUpdateDTO.setIsMaterialRelationRepeat(true);
                    //     // 标记存在需要处理的物料问题，后续会将整单状态设为结算异常
                    //     hasMaterialIssueNeedHandle = true;
                    // }
                }
            }
            
            // 添加到明细更新列表
            entryUpdateDTOs.add(entryUpdateDTO);
        }
        
        // 设置批量更新对象的明细列表
        batchUpdateDTO.setEntries(entryUpdateDTOs);
        
        // 根据处理结果决定下一步状态
        String nextSettleStatus;
        
        // 判断是否所有物料都被剔除且明细列表不为空
        // 当所有物料都被剔除时，需要将整单标记为剔除状态
        if (allMaterialsExcluded && !entryUpdateDTOs.isEmpty()) {
            // 所有物料都是剔除物料，标记为整单剔除
            log.info("[processDeliveryDetailOMS][单据编号: {} 所有物料都是剔除物料，标记为整单剔除]", deliveryDetail.getBillNo());
            cqDeliveryDetailService.updateExcludeMaterialInOrderByBillNo(deliveryDetail.getBillNo(), "1");
            nextSettleStatus = "2"; // 结算异常
        } else if (hasMaterialIssueNeedHandle) {
            // 有物料不存在或物料关系重复的问题
            log.info("[processDeliveryDetailOMS][单据编号: {} 存在物料不存在或物料关系重复且非剔除物料的情况，跳过结算]", deliveryDetail.getBillNo());
            nextSettleStatus = "2"; // 结算异常
        } else {
            // 正常情况，进入下一步结算
            nextSettleStatus = "3"; // 拆单明细待结算
        }
        batchUpdateDTO.setMainSettleStatus(nextSettleStatus);
        
        // 调用批量更新方法
        boolean updated = cqDeliveryDetailService.batchUpdateDeliveryDetailEntriesSettleInfo(batchUpdateDTO);
        if (!updated) {
            log.error("[processDeliveryDetailOMS][更新发货明细结算信息失败，单据编号: {}]", deliveryDetail.getBillNo());
        }
    }

    /**
     * 物料匹配方法（带批量物料列表的优化版本）
     * 按照以下优先级进行匹配：
     * ①将"货品编号"匹配【物料对应表】平台=E3/麦优E3，"业务平台对应货品编号"，取{苍穹物料}，若存在多条，则取默认
     * ②若仍匹配不到，将{货品编号}匹配【物料表】 {助记码}，取{物料编码}
     * ③若仍匹配不到，将{货品编号}匹配【物料表】 {物料编码}
     * ④若仍匹配不到，则将商品明细改行物料标识为物料不存在
     * 
     * @param platform 平台
     * @param materialNo 物料编号
     * @param wldygxEntriesMap 物料对应关系缓存映射
     * @param allApprovedMaterials 所有已审核物料列表（用于内存过滤优化）
     * @return 物料匹配结果
     */
    @Override
    public MaterialMatchResult matchOMSDetailMaterial(String platform, String materialNo, 
                                                    Map<PlatformMaterialKey, List<CqWldygxEntryDO>> wldygxEntriesMap,
                                                    List<CqBdMaterialDO> allApprovedMaterials) {
        MaterialMatchResult result = new MaterialMatchResult();
        
        // ①将"货品编号"匹配【物料对应表】平台=E3/麦优E3，"业务平台对应货品编号"，取{苍穹物料}，若存在多条，则优先取默认记录(isDefault=1)，若无唯一默认记录则标记重复
        // 使用缓存查找物料对应关系，如果缓存为空则返回空列表
        List<CqWldygxEntryDO> wldygxEntryDOs = (wldygxEntriesMap != null) 
            ? wldygxEntriesMap.getOrDefault(new PlatformMaterialKey(platform, materialNo), new ArrayList<>())
            : new ArrayList<>();
        
        if (wldygxEntryDOs == null || wldygxEntryDOs.size() <= 0) {
            // ②若仍匹配不到，将{货品编号}匹配【物料表】 {助记码}，取{物料编码}
            // 使用内存过滤，通过助记码匹配
            List<CqBdMaterialDO> materialList = allApprovedMaterials.stream()
                .filter(material -> materialNo.equals(material.getHelpCode()))
                .collect(Collectors.toList());
            
            // 助记码查找结果处理
            if (materialList.size() == 1) {
                // 找到唯一匹配：直接使用该物料ID
                result.setMaterialId(materialList.get(0).getId());
            } else if (materialList.size() > 1) {
                // 找到多个匹配：标记物料关系重复
                result.setIsMaterialRelationRepeat(1);
            } else {
                // ③若仍匹配不到，将{货品编号}匹配【物料表】 {物料编码}
                // 使用内存过滤，通过物料编码精确匹配
                CqBdMaterialDO material = allApprovedMaterials.stream()
                    .filter(mat -> materialNo.equals(mat.getNumber()))
                    .findFirst()
                    .orElse(null);
                
                // 精确匹配结果处理
                if (material != null) {
                    // 找到匹配：使用该物料ID
                    result.setMaterialId(material.getId());
                } else {
                    // ④若仍匹配不到，则将商品明细改行物料标识为物料不存在
                    // 所有查找方式均未找到匹配：标记物料不存在
                    result.setIsNotExitMaterial(1);
                }
            }
        } else if (wldygxEntryDOs.size() == 1) {
            // 找到唯一的物料对应关系：直接使用该物料ID
            result.setMaterialId(wldygxEntryDOs.get(0).getEasMaterialId());
        } else if (wldygxEntryDOs.size() > 1) {
            // 找到多个物料对应关系：优先选择默认记录，若无唯一默认记录则标记重复
            // 查找默认记录(isDefault=1)
            List<CqWldygxEntryDO> defaultEntries = wldygxEntryDOs.stream()
                    .filter(entry -> entry.getIsDefault() != null && entry.getIsDefault() == 1)
                    .collect(Collectors.toList());
            
            if (defaultEntries.size() == 1) {
                // 找到唯一默认记录：使用该物料ID
                result.setMaterialId(defaultEntries.get(0).getEasMaterialId());
            } else {
                // 多个默认记录或无默认记录：标记物料关系重复
                result.setIsMaterialRelationRepeat(1);
            }
        }
        
        return result;
    }

    /**
     * 拆单明细结算
     * 
     * 处理发货明细的拆单结算逻辑，包括：
     * 1. 验证发货明细是否满足拆单条件
     * 2. 处理物料匹配和组合关系
     * 3. 计算价格和内部交易关系
     * 4. 生成拆单结果并更新状态
     *
     * @param params 拆单结算参数对象，包含以下字段：
     *               - billNos: 单据编号列表，用于筛选需要处理的发货明细
     *               - shopNos: 店铺编号列表，用于筛选特定店铺的明细
     *               - startDate: 开始日期，用于筛选日期范围内的明细
     *               - endDate: 结束日期，用于筛选日期范围内的明细
     */
    @Override
    public void doSplitDetailSettle(DeliveryDetailSettleParamsDTO params) {
        // 参数校验和提取
        if (params == null) {
            log.warn("[doSplitDetailSettle][参数为空，使用默认参数]");
            params = new DeliveryDetailSettleParamsDTO();
        }
        
        List<String> billNos = params.getBillNos();
        List<String> shopNos = params.getShopNos();
        LocalDate startDate = params.getStartDate();
        LocalDate endDate = params.getEndDate();
        long methodStartTime = System.currentTimeMillis();
        
        // 参数校验和默认值设置
        if (startDate == null) {
            startDate = LocalDate.now().minusDays(31);
        }
        if (endDate == null) {
            endDate = LocalDate.now();
        }
        
        log.info("[doSplitDetailSettle][开始拆单明细结算，单据数量: {}, 店铺数量: {}, 日期范围: {} 到 {}]", 
                billNos != null ? billNos.size() : 0, 
                shopNos != null ? shopNos.size() : 0, startDate, endDate);
        
        try {
            // 1. 先统计各日期的记录数
            List<DeliveryDateStatDTO> dateStats = getDeliveryDateStatistics(billNos, "3", shopNos, startDate, endDate);
            
            if (CollUtil.isEmpty(dateStats)) {
                log.info("[doSplitDetailSettle][未找到符合条件的记录]");
                return;
            }
            
            // 2. 计算总记录数并记录统计信息
            long totalRecords = dateStats.stream().mapToLong(DeliveryDateStatDTO::getRecordCount).sum();
            log.info("[doSplitDetailSettle][统计完成，共 {} 个日期，总记录数: {}]", dateStats.size(), totalRecords);
            
            // 3. 检查总记录数是否超过警告阈值
            if (totalRecords > TOTAL_RECORDS_WARNING_THRESHOLD) {
                log.warn("[doSplitDetailSettle][总记录数({})超过警告阈值({}), 请注意内存使用]", 
                        totalRecords, TOTAL_RECORDS_WARNING_THRESHOLD);
            }
            
            // 4. 如果总记录数较少，使用原始查询方式
            if (totalRecords < LAYERED_QUERY_THRESHOLD) {
                log.info("[doSplitDetailSettle][记录数较少({})，使用原始查询方式]", totalRecords);
                executeOriginalSplitDetailSettle(billNos, shopNos, startDate, endDate);
                return;
            }
            
            // 5. 执行分层查询处理
            executeLayeredSplitDetailSettle(dateStats, billNos, shopNos, methodStartTime);
            
        } catch (Exception e) {
            log.error("[doSplitDetailSettle][分层查询异常，回退到原始查询]", e);
            executeOriginalSplitDetailSettle(billNos, shopNos, startDate, endDate);
        }
    }

    /**
     * 设置交付明细的仓库类型
     *
     * @param deliveryDetailEntry 交付明细条目
     * @param platform 平台
     * @param warehouseId 仓库ID
     * @param directwarehouseMap 直营店仓库缓存映射
     */
    private void setDeliveryEntryStockType(CqSourceDeliveryDetailEntryDO deliveryDetailEntry, String platform, Long warehouseId,
                                         Map<DirectwarehouseKey, List<CqDirectwarehouseDO>> directwarehouseMap) {
        // 使用批量查询结果进行快速查找
        DirectwarehouseKey key = new DirectwarehouseKey(platform, warehouseId);
        List<CqDirectwarehouseDO> cqDirectwarehouseDOs = directwarehouseMap.get(key);
        
        // 如果找到匹配的直营店仓库配置，则设置仓库类型
        if(cqDirectwarehouseDOs != null && cqDirectwarehouseDOs.size() > 0) {
            // 取第一个匹配的直营店仓库配置的仓库类型
            deliveryDetailEntry.setStockTypeId(cqDirectwarehouseDOs.get(0).getWarehouseType());
        }
    }

    /**
     * 根据品牌分单策略设置交付明细的销售组织ID
     * 
     * 业务逻辑说明：
     * 1. 如果不按品牌分单（isBrandSplitBill=0），直接使用店铺组织ID作为销售组织ID
     * 2. 如果按品牌分单（isBrandSplitBill=1），需要通过内部交易关系表查找对应的公司编码：
     *    - 首先根据物料ID查找物料信息，获取品牌ID、产品类型、物料类型
     *    - 然后使用客户ID+品牌ID+产品类型+物料类型作为组合键查找内部交易关系
     *    - 如果找到匹配记录，使用其一级组织ID作为销售组织ID
     *    - 如果未找到匹配记录，记录警告日志
     *
     * @param deliveryDetailEntry 交付明细条目 - 需要设置销售组织ID的目标对象
     * @param isBrandSplitBill 是否按品牌分单 - 0:否, 1:是
     * @param shopOrgId 店铺组织ID - 当不按品牌分单时使用
     * @param cqCustomerId 苍穹客户ID - 用于查找内部交易关系
     * @param materialId 物料ID - 用于获取物料的品牌、类型等信息
     * @param allApprovedMaterials 所有已审核物料列表 - 内存缓存，避免重复数据库查询
     * @param innerRelCombinedMap 内部交易关系组合缓存 - 预加载的内部交易关系映射表
     */
    private void setDeliveryEntrySaleOrgId(CqSourceDeliveryDetailEntryDO deliveryDetailEntry, Integer isBrandSplitBill,
                             Long shopOrgId, Long cqCustomerId, Long materialId, List<CqBdMaterialDO> allApprovedMaterials,
                             Map<InnerRelKey, InnerRelCombinedInfo> innerRelCombinedMap) {
        // 新逻辑（2025-07-01）
        // 将{结算信息-客户}+{拆单明细-品牌}+{拆单明细-物料类型}+{拆单明细-产品类型}匹配【内部交易关系表】{原客户编码}+{品牌}+{物料类型}+{产品类型}取{公司}
        // 其中若【内部交易关系表】品牌、物料类型、产品类型包含多个，则同一个字段多个值视为或的关系

        // ①是否按品牌分单为否，取"店铺组织"
        if(isBrandSplitBill == 0){
            // 不按品牌分单时，直接使用店铺组织ID
            deliveryDetailEntry.setSaleOrgId(shopOrgId);
        }else{
            // ②是否按品牌分单为是，将 店铺编码 匹配 店铺客户 对应表 "平台店铺"，取"苍穹客户编码"，
            // 再将"苍穹客户编码"+"品牌"匹配【内部交易关系表】，取"公司编码"
            
            // 从内存缓存中查找物料信息，避免数据库查询
            CqBdMaterialDO material = findMaterialInMemory(materialId, allApprovedMaterials);
            if (material == null) {
                // 物料信息不存在时记录警告并退出
                log.warn("[setDeliveryEntrySaleOrgId][物料ID: {} 未找到物料信息]", materialId);
                return;
            }
            
            // 构建内部交易关系查询键：客户ID + 品牌ID + 产品类型 + 物料类型
            // 使用内存Map替换数据库查询，提高性能
            InnerRelKey key = new InnerRelKey(cqCustomerId, material.getBrandId(), material.getProType(), material.getMatGroupId());
            InnerRelCombinedInfo combinedInfo = innerRelCombinedMap.get(key);
            
            // 如果找到匹配的内部交易关系记录且一级组织ID不为空
            if (combinedInfo != null && combinedInfo.getFirstLevelOrgId() != null) {
                // 设置销售组织ID为内部交易关系表中的一级组织ID（公司编码）
                deliveryDetailEntry.setSaleOrgId(combinedInfo.getFirstLevelOrgId());
            } else {
                // 未找到匹配记录或一级组织ID为空时记录警告
                log.warn("[setDeliveryEntrySaleOrgId][客户ID: {}, 品牌ID: {} 未找到内部交易关系记录或一级组织ID]", cqCustomerId, material.getBrandId());
            }
        }
    }

    /**
     * 获取拆单客户ID
     *
     * @param isNotSale 是否麦优非销
     * @param isBrandSplitBill 是否按品牌分单
     * @param cqCustomerId 苍穹客户ID
     * @param materialId 物料ID
     * @param allApprovedMaterials 所有已审核物料列表
     * @param innerRelCombinedMap 内部交易关系组合缓存
     * @return 拆单客户ID
     */
    private Long getSplitCustomerId(Integer isNotSale, Integer isBrandSplitBill, Long cqCustomerId, Long materialId, 
                                  List<CqBdMaterialDO> allApprovedMaterials, Map<InnerRelKey, InnerRelCombinedInfo> innerRelCombinedMap) {
        // 新逻辑（2025-07-01）
        // 将{结算信息-客户}+{拆单明细-品牌}+{拆单明细-物料类型}+{拆单明细-产品类型}匹配【内部交易关系表】{原客户编码}+{品牌}+{物料类型}+{产品类型}取{公司}
        // 其中若【内部交易关系表】品牌、物料类型、产品类型包含多个，则同一个字段多个值视为或的关系
                                    
        // ②客户不存在为否且麦优非销为否且是否按品牌分单为是，将 店铺编码 匹配 店铺客户 对应表 "平台店铺"，取"苍穹客户编码"，
        // 再将"苍穹客户编码"+"品牌"匹配【内部交易关系表】，取"新客户编码"
        if(isNotSale == 0 && isBrandSplitBill == 1) {
            // 从内存中查找物料信息
            CqBdMaterialDO material = findMaterialInMemory(materialId, allApprovedMaterials);
            if(material == null || material.getBrandId() == null) {
                log.warn("[getSplitCustomerId][物料ID: {} 未找到物料信息或品牌ID为空]", materialId);
                return 0L;
            }
            
            // 构建内部交易关系查询键，包含客户ID、品牌ID、产品类型、物料类型
            InnerRelKey key = new InnerRelKey(cqCustomerId, material.getBrandId(), material.getProType(), material.getMatGroupId());
            // 使用内存Map替换数据库查询，提高性能
            InnerRelCombinedInfo combinedInfo = innerRelCombinedMap.get(key);
            
            // 如果找到匹配的内部交易关系记录且新客户ID不为空，返回新客户ID
            if(combinedInfo != null && combinedInfo.getNewCustomerId() != null) {
                return combinedInfo.getNewCustomerId();
            }
            
            // 未找到匹配记录时记录警告日志并返回默认值0
            log.warn("[getSplitCustomerId][客户ID: {} 品牌ID: {} 未找到内部交易关系记录]", cqCustomerId, material.getBrandId());
            return 0L;
        }
        // ①客户不存在为否且麦优非销为否且是否按品牌分单为否，将 店铺编码 匹配 店铺客户 对应表 "平台店铺"，取"苍穹客户编码"
        // ③客户不存在为否且麦优非销为是，将 麦优非销店铺 匹配 店铺客户 对应表 "平台店铺"，取"苍穹客户编码"
        // 其他情况直接返回原始苍穹客户ID
        return cqCustomerId;
    }

    /**
     * 设置组装品的价格信息（重构后的接口）
     *
     * @param deliveryDetailEntry 交付明细条目对象
     * @param isBrandSplitBill 是否按品牌分单
     * @param entry 明细条目
     * @param billNo 单据编号
     * @param context 拆单上下文
     * @param materialId 组装品子物料ID
     * @param rate 占比率
     * @param omsOrderTotalAmount OMS订单总金额
     * @param isLastItem 是否最后一项
     * @param accumulator 金额累计追踪器
     * @param singleItemDirectAssign 是否单个子物料直接分配
     */
    private void setAssemblyPriceInfoV2(CqSourceDeliveryDetailEntryDO deliveryDetailEntry, 
                                    Integer isBrandSplitBill,
                                    CqDeliveryDetailEntryRespVO entry, 
                                    String billNo,
                                    OMSDetailSettleContext context,
                                    Long materialId,
                                    BigDecimal rate,
                                    BigDecimal omsOrderTotalAmount,
                                    boolean isLastItem,
                                    AmountAccumulator accumulator,
                                    boolean singleItemDirectAssign) {
        if(isBrandSplitBill == 1) {
            // 按品牌分单
            // 获取当前有效的经销商供货价格 - 使用内存过滤优化
            CqOrgCusPriceBillEntryDO dealerPrice = findValidPriceInMemory(
                materialId,
                deliveryDetailEntry.getCqCustomerId(), 
                deliveryDetailEntry.getSaleOrgId(), 
                context.getDeliveryDate(),
                context.getAllValidPrices());
                
            if(dealerPrice == null) {
                log.error("[setAssemblyPriceInfoV2][单据编号: {} 物料编号: {} 拆单客户ID: {} 销售组织ID: {} 交货日期: {} 当前有效价格为空]", 
                    billNo, materialId, deliveryDetailEntry.getCqCustomerId(), 
                    deliveryDetailEntry.getSaleOrgId(), context.getDeliveryDate());
                deliveryDetailEntry.setDistPriceNotExist(1);
            } else {
                // 单价
                deliveryDetailEntry.setTaxPrice(dealerPrice.getSalePrice());
                // 分销供货价
                deliveryDetailEntry.setDistSupplyPrice(dealerPrice.getSalePrice());
                // 总金额
                deliveryDetailEntry.setTotalTaxAmount(dealerPrice.getSalePrice().multiply(deliveryDetailEntry.getQuantity()));
                // 是否赠品
                if(deliveryDetailEntry.getTotalTaxAmount().compareTo(BigDecimal.ZERO) == 0) {
                    deliveryDetailEntry.setIsGift(1);
                }
            }
        } else {
            // 不按品牌分单 - 使用余额倒推法确保精度
            BigDecimal rateDecimal = rate.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);// 计算百分比
            
            BigDecimal itemAmount;
            BigDecimal avgLogisticsCost;
            BigDecimal shelvesPrice;
            BigDecimal totalDiscountAmount;
            BigDecimal splitShareAmount;
            
            // 处理原始金额为零的边界情况
            if (omsOrderTotalAmount.compareTo(BigDecimal.ZERO) == 0) {
                
                // 原始金额为零时，直接按比例计算，不使用余额倒推
                itemAmount = omsOrderTotalAmount.multiply(rateDecimal).setScale(2, RoundingMode.DOWN);
                avgLogisticsCost = entry.getAvLogisticsCost().multiply(rateDecimal).setScale(2, RoundingMode.DOWN);
                shelvesPrice = entry.getShelvesPrice();
                totalDiscountAmount = entry.getTotalDiscountAmount().multiply(rateDecimal).setScale(2, RoundingMode.DOWN);
                splitShareAmount = entry.getShareAmount().multiply(rateDecimal).setScale(2, RoundingMode.DOWN);
                
                log.debug("[setAssemblyPriceInfoV2][单据编号: {} 原始金额为零，按比例计算] 总金额: {}", billNo, itemAmount);
            } else if (singleItemDirectAssign) {
                // 边界情况：只有一个有效子物料，直接使用原始金额
                itemAmount = omsOrderTotalAmount;
                avgLogisticsCost = entry.getAvLogisticsCost();
                shelvesPrice = entry.getShelvesPrice();
                totalDiscountAmount = entry.getTotalDiscountAmount();
                splitShareAmount = entry.getShareAmount();
                
                log.debug("[setAssemblyPriceInfoV2][单据编号: {} 单个子物料直接分配] 总金额: {}, 物流成本: {}", 
                    billNo, itemAmount, avgLogisticsCost);
            } else if (isLastItem) {
                // 最后一项：使用余额倒推法
                itemAmount = omsOrderTotalAmount.subtract(accumulator.getTotalTaxAmount());
                avgLogisticsCost = entry.getAvLogisticsCost().subtract(accumulator.getAvgLogisticsCost());
                shelvesPrice = entry.getShelvesPrice();
                totalDiscountAmount = entry.getTotalDiscountAmount().subtract(accumulator.getTotalDiscountAmount());
                splitShareAmount = entry.getShareAmount().subtract(accumulator.getSplitShareAmount());
                
                log.debug("[setAssemblyPriceInfoV2][单据编号: {} 最后一项余额倒推] 总金额: {} = {} - {}, 物流成本: {} = {} - {}", 
                    billNo, itemAmount, omsOrderTotalAmount, accumulator.getTotalTaxAmount(),
                    avgLogisticsCost, entry.getAvLogisticsCost(), accumulator.getAvgLogisticsCost());
            } else {
                // 前N-1项：正常按比例计算
                itemAmount = omsOrderTotalAmount.multiply(rateDecimal).setScale(2, RoundingMode.HALF_UP);
                avgLogisticsCost = entry.getAvLogisticsCost().multiply(rateDecimal).setScale(2, RoundingMode.HALF_UP);
                shelvesPrice = entry.getShelvesPrice();
                totalDiscountAmount = entry.getTotalDiscountAmount().multiply(rateDecimal).setScale(2, RoundingMode.HALF_UP);
                splitShareAmount = entry.getShareAmount().multiply(rateDecimal).setScale(2, RoundingMode.HALF_UP);
                
                // 累计到追踪器中（只有在多个子物料且非最后一项时才累计）
                if (!singleItemDirectAssign) {
                    accumulator.addTotalTaxAmount(itemAmount);
                    accumulator.addAvgLogisticsCost(avgLogisticsCost);
                    accumulator.addTotalDiscountAmount(totalDiscountAmount);
                    accumulator.addSplitShareAmount(splitShareAmount);
                }
            }
            
            // 设置计算后的金额
            deliveryDetailEntry.setTotalTaxAmount(itemAmount);
            deliveryDetailEntry.setAvgLogisticsCost(avgLogisticsCost);
            deliveryDetailEntry.setShelvesPrice(shelvesPrice);
            deliveryDetailEntry.setTotalDiscountAmount(totalDiscountAmount);
            deliveryDetailEntry.setSplitShareAmount(splitShareAmount);
            
            // 单价
            if (deliveryDetailEntry.getQuantity().compareTo(BigDecimal.ZERO) != 0) {
                deliveryDetailEntry.setTaxPrice(itemAmount.divide(deliveryDetailEntry.getQuantity(), 2, RoundingMode.HALF_UP));
            }
            // 是否赠品
            if(itemAmount.compareTo(BigDecimal.ZERO) == 0) {
                deliveryDetailEntry.setIsGift(1);
            }
        }
    }

    /**
     * 设置非组装品的价格信息（重构后的接口）
     * 
     * 该方法用于为非组装品的交付明细条目设置价格相关信息，根据是否按品牌分单采用不同的价格计算策略：
     * - 按品牌分单：使用经销商供货价格进行计算
     * - 不按品牌分单：直接使用OMS订单的明细金额信息
     * 
     * @param deliveryDetailEntry 交付明细条目对象，用于设置价格信息
     * @param isBrandSplitBill 是否按品牌分单标识（1-按品牌分单，0-不按品牌分单）
     * @param entry OMS交付明细条目响应对象，包含原始价格数据
     * @param billNo 单据编号，用于日志记录和错误追踪
     * @param context OMS明细结算上下文，包含结算所需的各种缓存数据和配置信息
     */
    private void setNonGroupDeliveryEntryPriceInfoV2(CqSourceDeliveryDetailEntryDO deliveryDetailEntry,
                                        Integer isBrandSplitBill,
                                        CqDeliveryDetailEntryRespVO entry, 
                                        String billNo,
                                        OMSDetailSettleContext context) {
        if(isBrandSplitBill == 1) {
            setBrandSplitBillPriceInfoV2(deliveryDetailEntry, entry.getSingleMaterialId(), billNo, context);
        } else {
            // 总金额（价税合计）
            deliveryDetailEntry.setTotalTaxAmount(entry.getDetailTotalAmount());
            // 单价
            if (deliveryDetailEntry.getQuantity().compareTo(BigDecimal.ZERO) != 0) {
                deliveryDetailEntry.setTaxPrice(deliveryDetailEntry.getTotalTaxAmount().divide(deliveryDetailEntry.getQuantity(), 4, RoundingMode.HALF_UP));
            }
            // 是否赠品
            if(deliveryDetailEntry.getTotalTaxAmount().compareTo(BigDecimal.ZERO) == 0) {
                deliveryDetailEntry.setIsGift(1);
            }
            // 平均物流成本
            deliveryDetailEntry.setAvgLogisticsCost(entry.getAvLogisticsCost());
            // 商品上架单价
            deliveryDetailEntry.setShelvesPrice(entry.getShelvesPrice());
            // 商品总折扣
            deliveryDetailEntry.setTotalDiscountAmount(entry.getTotalDiscountAmount());
            // 均摊金额
            deliveryDetailEntry.setSplitShareAmount(entry.getShareAmount());
        }
    }

    /**
     * 设置按品牌分单的价格信息（重构后的接口）
     * 
     * 该方法用于为按品牌分单的交付明细条目设置价格相关信息，主要功能包括：
     * - 根据物料ID、客户ID、销售组织ID和交货日期查找有效的经销商供货价格
     * - 如果找到有效价格，设置单价、分销供货价、总金额等价格信息
     * - 如果未找到有效价格，标记分销价格不存在标识
     * - 根据总金额判断是否为赠品
     * 
     * @param deliveryDetailEntry 交付明细条目对象，用于设置价格信息
     * @param materialId 物料ID，用于查找对应的价格信息
     * @param billNo 单据编号，用于日志记录和错误追踪
     * @param context OMS明细结算上下文，包含交货日期和所有有效价格列表等缓存数据
     */
    private void setBrandSplitBillPriceInfoV2(CqSourceDeliveryDetailEntryDO deliveryDetailEntry, 
                                         Long materialId,
                                         String billNo,
                                         OMSDetailSettleContext context) {
        // 获取当前有效的经销商供货价格 - 使用内存过滤优化
        CqOrgCusPriceBillEntryDO dealerPrice = findValidPriceInMemory(
            materialId,
            deliveryDetailEntry.getCqCustomerId(), 
            deliveryDetailEntry.getSaleOrgId(), 
            context.getDeliveryDate(),
            context.getAllValidPrices());
            
        if(dealerPrice == null) {
            log.error("[setBrandSplitBillPriceInfoV2][单据编号: {} 物料编号: {} 拆单客户ID: {} 销售组织ID: {} 交货日期: {} 当前有效价格为空]", 
                billNo, materialId, deliveryDetailEntry.getCqCustomerId(), 
                deliveryDetailEntry.getSaleOrgId(), context.getDeliveryDate());
            deliveryDetailEntry.setDistPriceNotExist(1);
        } else {
            // 单价
            deliveryDetailEntry.setTaxPrice(dealerPrice.getSalePrice());
            // 分销供货价
            deliveryDetailEntry.setDistSupplyPrice(dealerPrice.getSalePrice());
            // 总金额
            deliveryDetailEntry.setTotalTaxAmount(dealerPrice.getSalePrice().multiply(deliveryDetailEntry.getQuantity()));
            // 是否赠品
            if(deliveryDetailEntry.getTotalTaxAmount().compareTo(BigDecimal.ZERO) == 0) {
                deliveryDetailEntry.setIsGift(1);
            }
        }
    }

    /**
     * 检查拆单明细是否存在问题
     * 
     * 该方法用于检查拆单明细分录中是否存在各种业务问题，包括：
     * - 组织相关问题：销售组织ID、库存组织ID是否缺失
     * - 仓库相关问题：销售组织仓库ID、库存组织仓库ID是否缺失
     * - 客户相关问题：客户ID是否缺失
     * - 物料相关问题：物料ID是否缺失
     * 
     * 业务逻辑说明：
     * 1. 首先过滤掉被标记为剔除的物料（isSplitExcludeMat=1）
     * 2. 检查过滤后是否还有可处理的明细
     * 3. 逐项检查各种必需字段是否缺失
     * 4. 将检查结果更新到结算信息对象中
     * 
     * @param mainId 主表ID - 用于标识需要更新结算信息的主记录
     * @param entries 拆单明细分录列表 - 需要检查的明细分录数据
     * @param billNo 单据编号 - 用于日志记录和问题追踪
     * @return 是否存在问题 - true表示存在问题，false表示正常
     */
    private boolean checkSourceDeliveryDetailEntryIssues(Long mainId, 
                                          List<CqSourceDeliveryDetailEntryDO> entries, 
                                          String billNo) {
        if (CollectionUtils.isEmpty(entries)) {
            log.warn("[checkSourceDeliveryDetailEntryIssues][单据编号: {} 没有可拆分的明细]", billNo);
            return true;
        }

        // 过滤掉剔除物料为是的数据
        List<CqSourceDeliveryDetailEntryDO> filteredEntries = entries.stream()
            .filter(entry -> entry.getIsSplitExcludeMat() == null || 
                           !Integer.valueOf(1).equals(entry.getIsSplitExcludeMat()))
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filteredEntries)) {
            log.warn("[checkSourceDeliveryDetailEntryIssues][单据编号: {} 过滤剔除物料后没有可拆分的明细，原始明细数: {}，过滤后明细数: 0]", billNo, entries.size());
            return true;
        }

        CqDeliveryDetailSettleInfoVO settleInfo = new CqDeliveryDetailSettleInfoVO();
        settleInfo.setId(mainId);

        // 检查组织相关问题
        // 检查是否存在销售组织ID为空或等于0的记录
        boolean hasMissingSaleOrgId = filteredEntries.stream()
                .anyMatch(entry -> entry.getSaleOrgId() == null || entry.getSaleOrgId() == 0);
        settleInfo.setIsNotExitSaleOrg(hasMissingSaleOrgId ? 1 : 0);

        // 检查是否存在库存组织ID为空或等于0的记录
        boolean hasMissingInvOrgId = filteredEntries.stream()
                .anyMatch(entry -> entry.getInvOrgId() == null || entry.getInvOrgId() == 0);
        settleInfo.setIsNotExitInvOrg(hasMissingInvOrgId ? 1 : 0);

        // 检查仓库相关问题
        // 检查是否存在销售组织仓库ID为空或等于0的记录
        boolean hasMissingSaleOrgStockId = filteredEntries.stream()
                .anyMatch(entry -> entry.getSaleOrgStockId() == null || entry.getSaleOrgStockId() == 0);
        settleInfo.setIsNotExitSaleOrgStock(hasMissingSaleOrgStockId ? 1 : 0);

        // 检查是否存在库存组织仓库ID为空或等于0的记录
        boolean hasMissingInvOrgStockId = filteredEntries.stream()
                .anyMatch(entry -> entry.getInvOrgStockId() == null || entry.getInvOrgStockId() == 0);
        settleInfo.setIsNotExitInvOrgStock(hasMissingInvOrgStockId ? 1 : 0);

        // 检查客户相关问题
        // 检查是否存在客户ID为空或等于0的记录
        boolean hasMissingCqCustomerId = filteredEntries.stream()
                .anyMatch(entry -> entry.getCqCustomerId() == null || entry.getCqCustomerId() == 0);
        settleInfo.setIsNotExitCustomer(hasMissingCqCustomerId ? 1 : 0);

        // 检查物料相关问题
        // 检查是否存在物料ID为空或等于0的记录
        boolean hasMissingMaterialId = filteredEntries.stream()
                .anyMatch(entry -> entry.getMaterialId() == null || entry.getMaterialId() == 0);
        settleInfo.setIsNotExitMaterial(hasMissingMaterialId ? 1 : 0);

        // 检查价格相关问题
        // 检查是否存在分销供货价不存在的记录
        boolean hasMissingDistPrice = filteredEntries.stream()
                .anyMatch(entry -> entry.getDistPriceNotExist() != null && entry.getDistPriceNotExist().equals(1));

        cqDeliveryDetailService.updateDeliveryDetailSettleInfo(settleInfo);

        return hasMissingSaleOrgId || hasMissingSaleOrgStockId 
               || hasMissingCqCustomerId || hasMissingMaterialId || hasMissingDistPrice 
               || hasMissingInvOrgId || hasMissingInvOrgStockId;
    }

    /**
     * 保存拆单明细分录并更新结算状态
     * @param mainId 主表ID
     * @param billNo 单据编号
     * @param entries 拆单明细分录列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveAndUpdateSettleStatus(Long mainId, String billNo, List<CqSourceDeliveryDetailEntryDO> entries) {
        // 删除拆单明细分录
        boolean isDeleteSuccess = cqDeliveryDetailService.deleteSourceDeliveryDetailEntries(mainId);
        if (!isDeleteSuccess) {
            log.info("[saveAndUpdateSettleStatus][单据编号: {} 删除拆单明细分录失败]", billNo);
            throw new RuntimeException("删除拆单明细分录失败");
        }
        
        // 保存拆单明细分录
        cqDeliveryDetailService.batchSaveSourceDeliveryDetailEntries(entries);

        // 检查拆单明细是否存在问题
        boolean hasIssues = checkSourceDeliveryDetailEntryIssues(mainId, entries, billNo);

        if (!hasIssues) {
            // 更新结算状态为"4"，表示已完成拆单明细结算
            cqDeliveryDetailService.updateSettleStatusByBillNo(billNo, "4","C",  "");
        } else {
            // 精确定位失败原因
            List<String> reasons = new ArrayList<>();
            reasons.add("拆单明细存在问题");
            cqDeliveryDetailService.updateSettleStatusByBillNo(billNo, "3", null, String.join("、", reasons));
        }
    }

    /**
     * 验证发货明细是否满足拆单条件
     * @param deliveryDetail 发货明细
     * @param reasons 不满足条件的原因列表
     * @return 是否满足拆单条件
     */
    private boolean validateDeliveryDetail(CqDeliveryDetailRespVO deliveryDetail, List<String> reasons) {
        // 验证是否整单剔除仓库
        if(deliveryDetail.getExcludeWarehouse() == 1) {
            log.info("[doSplitDetailSettle][单据编号: {} 整单剔除仓库]", deliveryDetail.getBillNo());
            reasons.add("整单剔除仓库");
            return false;
        }
        
        // 验证是否存在可结算的明细
        List<CqDeliveryDetailEntryRespVO> entries = deliveryDetail.getEntries();
        if (CollectionUtils.isEmpty(entries) || entries.size() <= 0) {
            log.error("[doSplitDetailSettle][单据编号: {} 没有可结算的发货明细]", deliveryDetail.getBillNo());
            return false;
        }
        
        // 验证明细是否存在物料不存在或物料对应关系重复的情况
        boolean canSettleSplitDetail = true;
        for (CqDeliveryDetailEntryRespVO entry : entries) {
            // 物料不存在
            String isNotExitMaterial = entry.getMxMaterialNotExist();
            if (isNotExitMaterial != null && "1".equals(isNotExitMaterial)) {
                log.error("[doSplitDetailSettle][单据编号: {} 物料不存在]", deliveryDetail.getBillNo());
                reasons.add("物料不存在");
                canSettleSplitDetail = false;
            }
            // 物料对应关系重复
            String isMaterialRelationRepeat = entry.getMxMaterialRefDuplicated();
            if (isMaterialRelationRepeat != null && "1".equals(isMaterialRelationRepeat)) {
                log.error("[doSplitDetailSettle][单据编号: {} 物料对应关系重复]", deliveryDetail.getBillNo());
                reasons.add("物料对应关系重复");
                canSettleSplitDetail = false;
            }
        }
        
        return canSettleSplitDetail;
    }

    /**
     * 初始化拆单上下文信息（V2版本 - 使用缓存数据）
     * @param deliveryDetail 发货明细
     * @param cacheData 拆单结算缓存数据
     * @return 拆单上下文对象
     */
    private OMSDetailSettleContext initializeSplitContextV2(CqDeliveryDetailRespVO deliveryDetail, 
                                                          SplitSettleCacheData cacheData) {
        OMSDetailSettleContext context = new OMSDetailSettleContext();
        
        // 设置主表ID
        context.setMainId(deliveryDetail.getId());
        // 设置是否按品牌分单标识
        context.setIsBrandSplitBill(deliveryDetail.getIsBrandSplitBill());
        // 设置苍穹客户ID
        context.setCqCustomerId(deliveryDetail.getCqCustomerId());
        // 设置发货仓库
        context.setShippingWarehouseCode(deliveryDetail.getWarehouse());
        // 设置是否非销售标识
        context.setIsNotSale(deliveryDetail.getIsNotSale());
        // 设置店铺组织ID
        context.setShopOrgId(deliveryDetail.getShopOrg());
        // 设置转换后的平台标识
        context.setPlatform(deliveryDetail.getPlatform());
        // 设置发货日期
        context.setDeliveryDate(deliveryDetail.getDeliveryDate());
        // 设置所有已审核物料列表
        context.setAllApprovedMaterials(cacheData.getAllApprovedMaterials());
        // 设置所有有效价格列表
        context.setAllValidPrices(cacheData.getAllValidPrices());
        // 设置内部交易关系组合缓存
        context.setInnerRelCombinedMap(cacheData.getInnerRelCombinedMap());
        
        return context;
    }

    /**
     * 创建基础交付明细条目
     * @param mainId 主表ID
     * @param seq 序号
     * @param parentEntry 父级明细条目
     * @return 基础交付明细条目
     */
    private CqSourceDeliveryDetailEntryDO createBaseDeliveryEntry(Long mainId, long seq, CqDeliveryDetailEntryRespVO parentEntry) {
        CqSourceDeliveryDetailEntryDO entryDO = new CqSourceDeliveryDetailEntryDO();
        entryDO.setMainId(mainId);
        entryDO.setEntryId(IdWorker.getId());
        entryDO.setSeq(seq);
        entryDO.setOmsRowNum(parentEntry.getSeq());
        entryDO.setOmsGoodsSn(parentEntry.getMaterialNo());
        return entryDO;
    }

    /**
     * 处理物料信息并检查是否为剔除物料
     * 
     * 该方法负责：
     * 1. 根据物料ID从内存中查找物料信息
     * 2. 检查物料是否在排除物料集合中
     * 3. 设置交付明细条目的物料相关字段
     * 4. 如果是剔除物料则跳过后续处理
     * 
     * @param entryDO 交付明细条目，用于设置物料相关信息
     * @param materialId 物料ID，用于查找物料信息
     * @param excludedMaterials 排除物料编码集合，用于快速判断是否为剔除物料
     * @param allApprovedMaterials 所有已审核物料列表，用于内存查找避免数据库查询
     * @return 物料对象(如果是剔除物料或不存在返回null)
     */
    private CqBdMaterialDO processItemMaterialInfo(CqSourceDeliveryDetailEntryDO entryDO, Long materialId, Set<String> excludedMaterials, List<CqBdMaterialDO> allApprovedMaterials) {
        // 从内存中查找物料信息，避免数据库查询提升性能
        CqBdMaterialDO material = findMaterialInMemory(materialId, allApprovedMaterials);
        
        if (material != null) {
            // 获取物料编号用于排除物料检查
            String materialNumber = material.getNumber();
            
            // 使用传入的排除物料集合进行快速查找，避免数据库查询
            boolean isExcludedMaterial = excludedMaterials.contains(materialNumber);
            
            // 设置是否为拆单排除物料标识（1-是，0-否）
            entryDO.setIsSplitExcludeMat(isExcludedMaterial ? 1 : 0);
            
            // 如果是剔除物料，记录日志并返回null，跳过后续处理
            if (isExcludedMaterial) {
                log.info("[processItemMaterialInfo][物料编号: {} 为剔除物料，跳过处理]", materialNumber);
                return null;
            }
            
            // 设置物料相关信息到交付明细条目
            // 品牌ID
            entryDO.setBrandid(material.getBrandId());
            // 产品类型
            entryDO.setProductType(material.getProType());
            // 物料类型ID
            entryDO.setMatgroupId(material.getMatGroupId());
        } else {
            // 物料不存在时记录错误日志并设置默认品牌ID
            log.error("[processItemMaterialInfo][物料ID: {} 未找到物料信息]", materialId);
            entryDO.setBrandid(0L);
        }
        
        // 返回物料对象，供后续处理使用
        return material;
    }

    /**
     * 从内存中查找物料信息
     * @param materialId 物料ID
     * @param allApprovedMaterials 所有已审核物料列表
     * @return 物料对象，如果未找到返回null
     */
    private CqBdMaterialDO findMaterialInMemory(Long materialId, List<CqBdMaterialDO> allApprovedMaterials) {
        return allApprovedMaterials.stream()
            .filter(material -> material.getId().equals(materialId))
            .findFirst()
            .orElse(null);
    }

    /**
     * 从内存中查找有效价格信息（经销商供货价）
     * 
     * @param materialId 物料ID
     * @param customerId 客户ID
     * @param orgId 组织ID
     * @param date 日期
     * @param allValidPrices 所有有效价格列表
     * @return 匹配的价格信息，如果未找到返回null
     */
    private CqOrgCusPriceBillEntryDO findValidPriceInMemory(Long materialId, Long customerId, Long orgId, 
                                                          LocalDateTime date, List<CqOrgCusPriceBillEntryDO> allValidPrices) {
        if (date == null) {
            date = LocalDateTime.now();
        }
        
        final LocalDateTime queryDate = date;
        
        return allValidPrices.stream()
            .filter(price -> materialId.equals(price.getMaterielId()))
            .filter(price -> customerId.equals(price.getCustomerId()))
            .filter(price -> orgId.equals(price.getOrgId()))
            .filter(price -> {
                // 检查有效期：开始日期 <= 查询日期
                if (price.getBeginDate() != null && price.getBeginDate().isAfter(queryDate)) {
                    return false;
                }
                // 检查有效期：查询日期 <= 结束日期 或 结束日期为空
                return price.getEndDate() == null || !price.getEndDate().isBefore(queryDate);
            })
            .sorted((p1, p2) -> {
                // 按生效日期降序排序，取最新的价格
                if (p1.getBeginDate() == null && p2.getBeginDate() == null) return 0;
                if (p1.getBeginDate() == null) return 1;
                if (p2.getBeginDate() == null) return -1;
                return p2.getBeginDate().compareTo(p1.getBeginDate());
            })
            .findFirst()
            .orElse(null);
    }

    /**
     * 处理组装品的拆单逻辑（V2版本 - 使用缓存数据）
     * @param context 拆单上下文
     * @param parentEntry 父级明细条目
     * @param seq 序号引用(包装为引用对象便于值传递)
     * @param cacheData 拆单结算缓存数据
     * @return 处理后的拆单明细列表
     */
    private List<CqSourceDeliveryDetailEntryDO> processAssemblyItemV2(OMSDetailSettleContext context, 
                                          CqDeliveryDetailEntryRespVO parentEntry, 
                                          AtomicLong seq,
                                          SplitSettleCacheData cacheData) {
        List<CqSourceDeliveryDetailEntryDO> result = new ArrayList<>();
        // 获取组合明细 - 使用BOM结构缓存
        CqBomWithEntriesDTO bomWithEntriesDTO = cacheData.getBomStructuresMap().getOrDefault(parentEntry.getMaterialNo(), null);
        List<CqBomEntryDO> combinations = bomWithEntriesDTO != null ? bomWithEntriesDTO.getEntries() : new ArrayList<>();
        
        // if (combinations.size() != 1) {
        //     throw new RuntimeException("组套物料数量不等于1，物料编号：" + parentEntry.getMaterialNo() + "，物料ID：" + parentEntry.getSingleMaterialId());
        // }
        
        List<CqBomEntryDO> subEntries = combinations;
        subEntries.sort(Comparator.comparing(CqBomEntryDO::getSeq));
        
        // // 计算组套总金额
        // BigDecimal combinationTotalAmount = calculateCombinationTotalAmount(subEntries);
        // OMS订单总金额
        BigDecimal omsOrderTotalAmount = parentEntry.getDetailTotalAmount();
        
        // 初始化金额累计追踪器，用于精度控制
        AmountAccumulator accumulator = new AmountAccumulator();
        
        // 预先计算有效子物料数量（用于确定最后一项）
        int validSubEntryCount = 0;
        for (CqBomEntryDO childEntry : subEntries) {
            // 从内存中查找物料信息
            CqBdMaterialDO material = findMaterialInMemory(childEntry.getMaterialId(), context.getAllApprovedMaterials());
            if (material != null && !cacheData.getExcludedMaterials().contains(material.getNumber())) {
                validSubEntryCount++;
            }
        }
        
        // 处理边界情况：如果只有一个有效子物料，直接使用原始金额
        boolean singleItemDirectAssign = (validSubEntryCount == 1);
        
        // 记录处理信息
        log.debug("[processAssemblyItemV2][单据编号: {} 组装品拆分开始] 总金额: {}, 有效子物料数: {}, 单项直接分配: {}", 
            context.getBillNo(), omsOrderTotalAmount, validSubEntryCount, singleItemDirectAssign);
        
        // 当前处理的有效子物料索引
        int currentValidIndex = 0;
        
        // 处理每个子物料
        for (CqBomEntryDO childEntry : subEntries) {
            CqSourceDeliveryDetailEntryDO entryDO = createBaseDeliveryEntry(context.getMainId(), seq.getAndIncrement(), parentEntry);
            
            // 设置物料ID
            entryDO.setMaterialId(childEntry.getMaterialId());
            
            // 处理物料品牌和剔除物料逻辑，获取物料对象
            CqBdMaterialDO material = processItemMaterialInfo(entryDO, childEntry.getMaterialId(), cacheData.getExcludedMaterials(), context.getAllApprovedMaterials());
            if (material == null) {
                // 如果是剔除物料或物料不存在，添加到结果后继续下一个
                result.add(entryDO);
                continue;
            }
            
            // 增加有效子物料索引
            currentValidIndex++;
            
            // 判断是否为最后一个有效子物料
            boolean isLastItem = (currentValidIndex == validSubEntryCount);
            
            // 获取拆单客户ID
            // 根据是否非销售、是否品牌拆单、原客户ID、物料ID等信息确定拆单后的客户ID
            // 如果是品牌拆单，会根据物料品牌信息重新分配客户ID
            // 如果是非销售订单，会使用特定的非销售客户ID
            Long splitCustomerId = getSplitCustomerId(
                context.getIsNotSale(),          // 是否非销售订单
                context.getIsBrandSplitBill(),   // 是否品牌拆单
                context.getCqCustomerId(),       // 原客户ID
                material.getId(),                // 物料ID
                context.getAllApprovedMaterials(), // 所有已审核物料信息
                context.getInnerRelCombinedMap()   // 内部关联组合映射关系
            );
            // 设置拆单后的客户ID到明细条目
            entryDO.setCqCustomerId(splitCustomerId);
            
            // 设置销售组织ID
            // 根据品牌拆单标识、店铺组织ID、客户ID、物料ID等信息确定销售组织ID
            // 对于组装品的子物料，需要根据子物料的品牌信息来确定对应的销售组织
            setDeliveryEntrySaleOrgId(
                entryDO, 
                context.getIsBrandSplitBill(), 
                context.getShopOrgId(), 
                context.getCqCustomerId(), 
                material.getId(),
                context.getAllApprovedMaterials(),
                context.getInnerRelCombinedMap()
            );
            
            // 计算数量
            entryDO.setQuantity(parentEntry.getE3Qty().multiply(childEntry.getQty()));
            
            // 设置组装品价格信息 - 使用V2版本
            // 为组装品的子物料设置价格相关信息，包括：
            // 1. 单价计算：根据子物料在组装品中的比例分摊价格
            // 2. 总金额计算：单价 × 数量
            // 3. 品牌拆单处理：如果启用品牌拆单，需要按品牌重新分配价格
            // 4. 价格分摊策略：基于子物料的比例(rate)和OMS订单总金额进行分摊
            setAssemblyPriceInfoV2(
                entryDO,                         // 交付明细条目对象，用于设置价格信息
                context.getIsBrandSplitBill(),   // 是否品牌拆单标识
                parentEntry,                     // 父级组装品条目信息
                context.getBillNo(),             // 单据编号，用于日志记录
                context,                         // 拆单上下文信息
                material.getId(),                // 子物料ID
                childEntry.getProp(),            // 子物料在组装品中的比例
                omsOrderTotalAmount,             // OMS订单总金额，用于价格分摊计算
                isLastItem,                      // 是否最后一项
                accumulator,                     // 金额累计追踪器
                singleItemDirectAssign           // 是否单个子物料直接分配
            );
            
            // 配置仓库设置信息
            configureWarehouseSettings(entryDO, context, splitCustomerId, cacheData);
            
            // 是否组装品
            entryDO.setIsAssembly(parentEntry.getIsAssembly());
            
            // 设置拆单失败原因 - 已删除setSrcError字段映射，因为DO中不存在此字段
            // if(entryDO.getSaleOrgId() == null || entryDO.getSaleOrgId() == 0) {
            //     entryDO.setSrcError("销售组织为空");
            // } else {
            //     entryDO.setSrcError("");
            // }
            
            result.add(entryDO);
        }
        
        // 最终验证：计算所有拆分项的总金额，确保与原始金额一致
        if (validSubEntryCount > 1) {
            BigDecimal totalSplitAmount = result.stream()
                .filter(entry -> entry.getTotalTaxAmount() != null)
                .map(CqSourceDeliveryDetailEntryDO::getTotalTaxAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            BigDecimal difference = totalSplitAmount.subtract(omsOrderTotalAmount).abs();
            if (difference.compareTo(new BigDecimal("0.01")) > 0) {
                log.warn("[processAssemblyItemV2][单据编号: {} 精度验证失败] 原始金额: {}, 拆分后汇总: {}, 差异: {}", 
                    context.getBillNo(), omsOrderTotalAmount, totalSplitAmount, difference);
            } else {
                log.debug("[processAssemblyItemV2][单据编号: {} 精度验证通过] 原始金额: {}, 拆分后汇总: {}, 差异: {}", 
                    context.getBillNo(), omsOrderTotalAmount, totalSplitAmount, difference);
            }
        }
        
        return result;
    }

    /**
     * 配置仓库设置信息
     * 
     * 该方法统一处理交付明细条目的仓库相关设置，包括：
     * 1. 库存组织仓库ID设置：根据平台、发货仓库、品牌、物料类型、产品类型匹配仓库对应关系
     * 2. 库存组织ID设置：根据库存组织仓库ID获取对应的仓库创建组织
     * 3. 销售组织仓库ID设置：根据库存组织仓库、品牌、客户等信息匹配委外品牌仓库映射
     * 4. 仓库类型设置：根据平台和仓库ID设置仓库类型
     * 
     * 业务逻辑说明：
     * - 库存组织仓库匹配：平台+发货仓库编码+品牌+物料类型+产品类型 -> 苍穹仓库ID
     * - 销售组织仓库匹配：库存组织仓库+品牌+客户+物料类型+产品类型 -> 实际仓库ID
     * - 支持回退策略：当包含客户的查询失败时，使用客户ID=0的通用配置
     * 
     * @param entryDO 交付明细条目对象，用于设置仓库相关字段
     * @param context 拆单上下文信息，包含平台、发货仓库编码等信息
     * @param splitCustomerId 拆单客户ID，用于销售组织仓库映射查询
     * @param cacheData 拆单结算缓存数据，包含各种仓库映射关系缓存
     */
    private void configureWarehouseSettings(CqSourceDeliveryDetailEntryDO entryDO,
                                           OMSDetailSettleContext context, 
                                           Long splitCustomerId,
                                           SplitSettleCacheData cacheData) {
        // ========== 设置库存组织仓库ID ==========
        // 业务逻辑：将{基本信息-发货仓库编码}+{拆单明细-品牌}+{拆单明细-物料类型}+{拆单明细-产品类型}
        // 匹配【仓库对应表】{业务平台对应仓库}+{品牌}+{物料类型}+{产品类型}取{苍穹仓库}
        // 注意：若【仓库对应表】品牌、物料类型、产品类型包含多个，则同一个字段多个值视为或的关系
        
        // 1. 构建仓库对应关系查询键
        PlatformWarehouseKey warehouseKey = new PlatformWarehouseKey(
            context.getPlatform(),                  // 业务平台
            context.getShippingWarehouseCode(),     // 发货仓库编码
            entryDO.getProductType(),               // 产品类型
            entryDO.getBrandid(),                   // 品牌ID
            entryDO.getMatgroupId()                 // 物料类型ID
        );
        
        // 2. 从缓存中获取仓库对应关系
        List<CqCkdygxEntryDO> ckdygxEntries = cacheData.getCkdygxEntriesMap().get(warehouseKey);
        
        if (ckdygxEntries != null && !ckdygxEntries.isEmpty()) {
            // 3. 获取第一个匹配的仓库对应关系（通常只有一个）
            CqCkdygxEntryDO ckdygxEntry = ckdygxEntries.get(0);
            
            // 4. 设置库存组织仓库ID为匹配到的苍穹仓库ID
            entryDO.setInvOrgStockId(ckdygxEntry.getWarehouseId());

            // 5. 根据库存组织仓库ID获取对应的仓库信息，设置库存组织ID
            CqBdWarehouseDO cqBdWarehouseDO = cacheData.getWarehouseMap().get(ckdygxEntry.getWarehouseId());
            if (cqBdWarehouseDO != null) {
                // 使用仓库的创建组织作为库存组织ID
                entryDO.setInvOrgId(cqBdWarehouseDO.getCreateOrgId());
                // 设置仓库类型
                setDeliveryEntryStockType(entryDO, context.getPlatform(), ckdygxEntry.getWarehouseId(), cacheData.getDirectwarehouseMap());
            } else {
                // 仓库信息未找到，记录警告并设置默认值
                log.warn("[configureWarehouseSettings][仓库ID: {} 在缓存中未找到，设置库存组织为默认值0]", ckdygxEntry.getWarehouseId());
                entryDO.setInvOrgId(0L);
            }

            // ========== 设置销售组织仓库ID ==========
            // 业务逻辑：将{拆单明细-库存组织仓库编码}+{拆单明细-品牌}+{拆单明细-物料类型}+{拆单明细-产品类型}+{客户}
            // 匹配【品牌品类仓库映射表】{库存组织仓库编码}+{品牌}+{物料类型}+{产品类型}+{客户}取{结算组织仓库编码}
            // 注意：若{拆单明细-客户}在映射表中找不到对应客户，则取其他维度匹配但客户为空的那条数据
            // 注意：若【仓库对应表】品牌、物料类型、产品类型包含多个，则同一个字段多个值视为或的关系
            
            // 6. 获取委外品牌仓库映射关系缓存
            Map<OutwarehouseMapKey, List<CqYdOutwarehousemapEntryDO>> outwarehouseMapEntries = cacheData.getOutwarehouseMapEntries();
            
            // 7. 构建委外仓库映射查询键（包含客户ID）
            OutwarehouseMapKey outwarehouseMapKey = new OutwarehouseMapKey(
                entryDO.getInvOrgStockId(),         // 库存组织仓库ID
                entryDO.getBrandid(),               // 品牌ID
                splitCustomerId,                    // 拆单客户ID
                entryDO.getMatgroupId(),            // 物料类型ID
                entryDO.getProductType()            // 产品类型
            );
            
            // 8. 尝试根据完整条件（包含客户）查找映射关系
            List<CqYdOutwarehousemapEntryDO> outwarehouseMapEntry = outwarehouseMapEntries.get(outwarehouseMapKey);
            
            if (outwarehouseMapEntry != null && !outwarehouseMapEntry.isEmpty()) {
                // 找到匹配的映射关系，设置销售组织仓库ID
                entryDO.setSaleOrgStockId(outwarehouseMapEntry.get(0).getActuralWarehouseId());
            } else {
                // 9. 若包含客户的查询未找到结果，则使用回退策略：客户ID设为0进行查询
                // 这是为了处理客户在映射表中不存在的情况，使用其他维度匹配但客户为空的数据
                OutwarehouseMapKey fallbackKey = new OutwarehouseMapKey(
                    entryDO.getInvOrgStockId(),     // 库存组织仓库ID
                    entryDO.getBrandid(),           // 品牌ID
                    0L,                             // 客户ID设为0（表示不限定客户）
                    entryDO.getMatgroupId(),        // 物料类型ID
                    entryDO.getProductType()        // 产品类型
                );
                
                // 10. 使用回退键再次查询
                outwarehouseMapEntry = outwarehouseMapEntries.get(fallbackKey);
                
                if (outwarehouseMapEntry != null && !outwarehouseMapEntry.isEmpty()) {
                    // 找到回退匹配的映射关系，设置销售组织仓库ID
                    entryDO.setSaleOrgStockId(outwarehouseMapEntry.get(0).getActuralWarehouseId());
                } else {
                    // 完全未找到匹配的映射关系，记录警告并设置默认值
                    log.warn("[configureWarehouseSettings][销售组织仓库映射未找到，查询键: {}，设置销售组织仓库为默认值0]", outwarehouseMapKey);
                    entryDO.setSaleOrgStockId(0L);
                }
            }
        } else {
            // 11. 仓库对应关系未找到的情况处理
            // 记录警告并设置所有相关字段为默认值
            log.warn("[configureWarehouseSettings][仓库对应关系未找到，查询键: {}，设置库存组织和销售组织仓库为默认值0]", warehouseKey);
            entryDO.setInvOrgId(0L);
            entryDO.setInvOrgStockId(0L);
            entryDO.setSaleOrgStockId(0L);
        }
    }

    /**
     * 处理非组装品的拆单逻辑（V2版本 - 使用缓存数据）
     * @param context 拆单上下文
     * @param entry 明细条目
     * @param seq 序号
     * @param cacheData 拆单结算缓存数据
     * @return 处理后的拆单明细条目
     */
    private CqSourceDeliveryDetailEntryDO processNonAssemblyItemV2(OMSDetailSettleContext context, 
                                    CqDeliveryDetailEntryRespVO entry, 
                                    long seq,
                                    SplitSettleCacheData cacheData) {
        CqSourceDeliveryDetailEntryDO entryDO = createBaseDeliveryEntry(context.getMainId(), seq, entry);
        
        // 设置物料ID
        entryDO.setMaterialId(entry.getSingleMaterialId());
        
        // 处理物料品牌和剔除物料逻辑，获取物料对象
        CqBdMaterialDO material = processItemMaterialInfo(entryDO, entry.getSingleMaterialId(), cacheData.getExcludedMaterials(), context.getAllApprovedMaterials());
        if (material == null) {
            // 如果是剔除物料或物料不存在，直接返回
            return entryDO;
        }
        
        // 设置数量
        entryDO.setQuantity(entry.getE3Qty());
        
        // 获取拆单客户ID
        // 根据是否非销售、是否品牌拆单、原客户ID、物料ID等信息确定拆单后的客户ID
        // 如果是品牌拆单，会根据物料品牌信息重新分配客户ID
        // 如果是非销售订单，会使用特定的非销售客户ID
        Long splitCustomerId = getSplitCustomerId(
            context.getIsNotSale(),          // 是否非销售订单
            context.getIsBrandSplitBill(),   // 是否品牌拆单
            context.getCqCustomerId(),       // 原客户ID
            material.getId(),                // 物料ID
            context.getAllApprovedMaterials(), // 所有已审核物料信息
            context.getInnerRelCombinedMap()   // 内部关联组合映射关系
        );
        // 设置拆单后的客户ID到明细条目
        entryDO.setCqCustomerId(splitCustomerId);
        
        // 设置销售组织ID
        // 根据是否品牌拆单、店铺组织ID、客户ID、物料ID等信息确定销售组织
        setDeliveryEntrySaleOrgId(
            entryDO, 
            context.getIsBrandSplitBill(), 
            context.getShopOrgId(), 
            context.getCqCustomerId(), 
            material.getId(),
            context.getAllApprovedMaterials(),
            context.getInnerRelCombinedMap()
        );
        
        // 设置价格信息 - 使用V2版本
        // 为非组装品设置价格相关信息，包括单价、总价等
        setNonGroupDeliveryEntryPriceInfoV2(
            entryDO, 
            context.getIsBrandSplitBill(), 
            entry, 
            context.getBillNo(), 
            context
        );

        // 配置仓库设置信息
        configureWarehouseSettings(entryDO, context, splitCustomerId, cacheData);
        
        // 是否组装品
        entryDO.setIsAssembly(entry.getIsAssembly());
        
        // 拆单失败原因 - 已删除setSrcError字段映射，因为DO中不存在此字段
        // if(entryDO.getSaleOrgId() == null || entryDO.getSaleOrgId() == 0) {
        //     entryDO.setSrcError("销售组织为空");
        // }
        
        return entryDO;
    }

    /**
     * 加载OMS结算所需的缓存数据
     * 
     * @return OMS结算缓存数据对象
     */
    private OMSSettleCacheData loadOMSSettleCacheData() {
        OMSSettleCacheData cacheData = new OMSSettleCacheData();
        cacheData.setCacheLoadStartTime(System.currentTimeMillis());
        
        try {
            // 批量查询平台合并匹配参数
            cacheData.setPlatformMergeParam(cqE3ParamSettingService.getParamValueByNumber("PlatformMergeMatchingTableOfE3"));
            
            // 批量查询所有已审核的排除物料编码
            cacheData.setExcludedMaterials(cqPcwlService.getAllExcludedMaterials());
            
            // // 批量查询所有物料组合关系
            cacheData.setBomStructuresMap(cqBomService.getAllAuditedBomStructuresMap());
            
            // 批量查询所有物料对应关系
            cacheData.setWldygxEntriesMap(cqWldygxService.getAllWldygxEntries());
            
            // 批量查询所有已审核的物料
            cacheData.setAllApprovedMaterials(cqBdMaterialService.getAllMaterialsWithGroup());
            
            // 批量查询所有有效价格数据
            cacheData.setAllValidPrices(cqOrgCusPriceBillService.getAllValidPrices());
            
            cacheData.setCacheLoadEndTime(System.currentTimeMillis());
            
            log.info("[loadOMSSettleCacheData][OMS结算缓存数据加载完成，耗时: {}ms, 排除物料: {}, BOM结构: {}, 物料对应: {}, 已审核物料: {}, 有效价格: {}]", 
                    cacheData.getCacheLoadDuration(), 
                    cacheData.getExcludedMaterials().size(),
                    cacheData.getBomStructuresMap().size(),
                    cacheData.getWldygxEntriesMap().size(),
                    cacheData.getAllApprovedMaterials().size(),
                    cacheData.getAllValidPrices().size());
            
            return cacheData;
        } catch (Exception e) {
            log.error("[loadOMSSettleCacheData][OMS结算缓存数据加载失败]", e);
            throw new RuntimeException("OMS缓存数据加载失败", e);
        }
    }

    /**
     * 加载主单结算所需的缓存数据
     * 
     * @return 主单结算缓存数据对象
     */
    private MainSettleCacheData loadMainSettleCacheData() {
        MainSettleCacheData cacheData = new MainSettleCacheData();
        cacheData.setCacheLoadStartTime(System.currentTimeMillis());
        
        try {
            // 批量查询平台合并匹配参数
            cacheData.setPlatformMergeParam(cqE3ParamSettingService.getParamValueByNumber("PlatformMergeMatchingTableOfE3"));
            
            // 批量查询所有排除仓库编码
            cacheData.setExcludedWarehouseCodes(cqPcckService.getAllExcludedWarehouseCodes());
            
            // 批量查询所有仓库对应关系
            cacheData.setCkdygxEntriesMap(cqCkdygxService.getAllCkdygxEntries());
            
            // 批量查询所有仓库信息
            cacheData.setWarehouseMap(cqBdWarehouseService.getAllWarehousesMap());
            
            // 批量预加载客户关系数据
            cacheData.setCustomerRelationEntriesMap(cqDBCustomerRelationService.getAllCustomerRelationEntriesMap());

            // 批量查询所有客户
            cacheData.setCustomerMap(cqCustomerService.getAllCustomersByNumberMap());

            // 批量查询所有店铺成本中心映射关系
            cacheData.setShopCostCenterMapDTOs(cqShopCostCenterMapService.getShopCostCenterMapWithRelations());

            cacheData.setCacheLoadEndTime(System.currentTimeMillis());
            
            log.info("[loadMainSettleCacheData][缓存数据加载完成，耗时: {}ms, 客户关系缓存键数量: {}]", 
                    cacheData.getCacheLoadDuration(), cacheData.getCustomerRelationEntriesMap().size());
            
            return cacheData;
        } catch (Exception e) {
            log.error("[loadMainSettleCacheData][缓存数据加载失败]", e);
            throw new RuntimeException("缓存数据加载失败", e);
        }
    }

    /**
     * 加载拆单结算所需的缓存数据
     * 
     * @return 拆单结算缓存数据对象
     */
    private SplitSettleCacheData loadSplitSettleCacheData() {
        SplitSettleCacheData cacheData = new SplitSettleCacheData();
        cacheData.setCacheLoadStartTime(System.currentTimeMillis());
        
        try {
            // 批量查询平台合并匹配参数
            cacheData.setPlatformMergeParam(cqE3ParamSettingService.getParamValueByNumber("PlatformMergeMatchingTableOfE3"));
            
            // 批量查询所有已审核的排除物料编码
            cacheData.setExcludedMaterials(cqPcwlService.getAllExcludedMaterials());

            // 批量查询所有委外品牌仓库映射关系
            cacheData.setOutwarehouseMapEntries(cqYdOutwarehousemapService.getAllOutwarehousemapEntries());
            
            // 批量查询所有已审核的物料
            cacheData.setAllApprovedMaterials(cqBdMaterialService.getAllMaterialsWithGroup());
            
            // 批量查询所有有效价格数据（经销商供货价）
            cacheData.setAllValidPrices(cqOrgCusPriceBillService.getAllValidPrices());
            
            // 批量查询所有内部交易关系数据
            cacheData.setInnerRelCombinedMap(cqInnerRelBillService.getAllInnerRelCombinedData());
            
            // 批量查询所有直营店仓库数据
            cacheData.setDirectwarehouseMap(cqDirectwarehouseService.getAllDirectwarehouseEntries());

            // 批量查询所有仓库对应关系
            cacheData.setCkdygxEntriesMap(cqCkdygxService.getAllCkdygxEntries());
            
            // 批量查询所有仓库信息
            cacheData.setWarehouseMap(cqBdWarehouseService.getAllWarehousesMap());
            
            // 批量查询所有销售BOM数据
            cacheData.setBomStructuresMap(cqBomService.getAllAuditedBomStructuresMap());
            
            cacheData.setCacheLoadEndTime(System.currentTimeMillis());
            
            log.info("[loadSplitSettleCacheData][拆单结算缓存数据加载完成，耗时: {}ms, 排除物料: {}, 委外仓库映射: {}, 已审核物料: {}, 有效价格: {}, 内部交易关系: {}, 直营店仓库: {}, 仓库对应关系: {}, 仓库信息: {}, 销售BOM: {}]", 
                    cacheData.getCacheLoadDuration(), 
                    cacheData.getExcludedMaterials().size(),
                    cacheData.getOutwarehouseMapEntries().size(),
                    cacheData.getAllApprovedMaterials().size(),
                    cacheData.getAllValidPrices().size(),
                    cacheData.getInnerRelCombinedMap().size(),
                    cacheData.getDirectwarehouseMap().size(),
                    cacheData.getCkdygxEntriesMap().size(),
                    cacheData.getWarehouseMap().size(),
                    cacheData.getBomStructuresMap().size());
            
            return cacheData;
        } catch (Exception e) {
            log.error("[loadSplitSettleCacheData][拆单结算缓存数据加载失败]", e);
            throw new RuntimeException("拆单结算缓存数据加载失败", e);
        }
    }

    /**
     * 处理单条发货明细的OMS结算
     * 
     * @param deliveryDetail 发货明细
     * @param cacheData OMS缓存数据
     * @param statistics 统计信息
     * @return 处理是否成功
     */
    private boolean processSingleOMSDeliveryDetail(CqDeliveryDetailRespVO deliveryDetail, 
                                                 OMSSettleCacheData cacheData, 
                                                 SettleStatistics statistics) {
        try {
            // 处理单个发货明细的OMS结算
            processDeliveryDetailOMS(deliveryDetail, cacheData);
            statistics.incrementSuccess();
            return true;
        } catch (Exception e) {
            statistics.incrementFail();
            log.error("[processSingleOMSDeliveryDetail][单据编号: {} 处理发货明细异常]", deliveryDetail.getBillNo(), e);
            
            // 发送钉钉异常通知
            sendExceptionNotification(deliveryDetail.getBillNo(), e, "processSingleOMSDeliveryDetail");
            
            try {
                cqDeliveryDetailService.updateSettleStatusByBillNo(deliveryDetail.getBillNo(), "2", null, e.toString());
            } catch (Exception updateException) {
                log.error("[processSingleOMSDeliveryDetail][更新结算状态失败，单据编号: {}]", deliveryDetail.getBillNo(), updateException);
            }
            return false;
        }
    }

    /**
     * 处理单条发货明细
     * 
     * @param deliveryDetail 发货明细
     * @param cacheData 缓存数据
     * @param statistics 统计信息
     * @return 处理是否成功
     */
    private boolean processSingleDeliveryDetail(CqDeliveryDetailRespVO deliveryDetail, 
                                              MainSettleCacheData cacheData, 
                                              SettleStatistics statistics) {
        List<String> reasons = new ArrayList<>();
        try {
            processDeliveryDetail(deliveryDetail, reasons, cacheData);
            statistics.incrementSuccess();
            return true;
        } catch (Exception e) {
            statistics.incrementFail();
            log.error("[processSingleDeliveryDetail][单据编号: {} 处理发货明细异常]", deliveryDetail.getBillNo(), e);
            
            // 发送钉钉异常通知
            sendExceptionNotification(deliveryDetail.getBillNo(), e, "processSingleDeliveryDetail");
            
            reasons.add("处理异常: " + e.getMessage());
            
            try {
                cqDeliveryDetailService.updateSettleStatusByBillNo(deliveryDetail.getBillNo(), "1", null, String.join("、", reasons));
            } catch (Exception updateException) {
                log.error("[processSingleDeliveryDetail][更新结算状态失败，单据编号: {}]", deliveryDetail.getBillNo(), updateException);
            }
            return false;
        }
    }

    /**
     * 批量处理OMS发货明细列表
     * 
     * @param deliveryDetails 发货明细列表
     * @param cacheData OMS缓存数据
     * @param methodName 调用方法名（用于日志记录）
     * @return 处理统计信息
     */
    private SettleStatistics processBatchOMSDeliveryDetails(List<CqDeliveryDetailRespVO> deliveryDetails,
                                                            OMSSettleCacheData cacheData,
                                                            String methodName) {
        SettleStatistics statistics = new SettleStatistics();
        
        if (CollectionUtils.isEmpty(deliveryDetails)) {
            log.info("[{}][没有可结算的发货明细]", methodName);
            statistics.markEnd();
            return statistics;
        }
        
        log.info("[{}][开始处理OMS明细结算，共{}条发货明细]", methodName, deliveryDetails.size());
        
        // 遍历发货明细，进行结算
        for (CqDeliveryDetailRespVO deliveryDetail : deliveryDetails) {
            processSingleOMSDeliveryDetail(deliveryDetail, cacheData, statistics);
        }
        
        statistics.markEnd();
        log.info("[{}][OMS明细结算完成，{}]", methodName, statistics.getSummary());
        
        return statistics;
    }

    /**
     * 批量处理发货明细列表
     * 
     * @param deliveryDetails 发货明细列表
     * @param cacheData 缓存数据
     * @param methodName 调用方法名（用于日志记录）
     * @return 处理统计信息
     */
    private SettleStatistics processBatchDeliveryDetails(List<CqDeliveryDetailRespVO> deliveryDetails,
                                                         MainSettleCacheData cacheData,
                                                         String methodName) {
        SettleStatistics statistics = new SettleStatistics();
        
        if (CollectionUtils.isEmpty(deliveryDetails)) {
            log.info("[{}][没有可结算的发货明细]", methodName);
            statistics.markEnd();
            return statistics;
        }
        
        log.info("[{}][开始处理主单结算，共{}条发货明细]", methodName, deliveryDetails.size());
        
        // 遍历发货明细，进行结算
        for (CqDeliveryDetailRespVO deliveryDetail : deliveryDetails) {
            processSingleDeliveryDetail(deliveryDetail, cacheData, statistics);
        }
        
        statistics.markEnd();
        log.info("[{}][主单结算完成，{}]", methodName, statistics.getSummary());
        
        return statistics;
    }

    /**
     * 获取发货日期统计信息
     * 
     * @param billNos 单据编号列表
     * @param settleStatus 结算状态
     * @param shopNos 店铺编号列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日期统计信息列表
     */
    private List<DeliveryDateStatDTO> getDeliveryDateStatistics(List<String> billNos, String settleStatus, 
                                                               List<String> shopNos, LocalDate startDate, LocalDate endDate) {
        try {
            LocalDateTime startDateTime = startDate.atStartOfDay();
            LocalDateTime endDateTime = endDate.atTime(23, 59, 59);
            
            List<Map<String, Object>> rawStats = deliveryDetailMapper.countByDeliveryDateGrouped(
                    billNos, settleStatus, shopNos, startDateTime, endDateTime);
            
            return rawStats.stream()
                    .map(map -> {
                        LocalDate date = ((java.sql.Date) map.get("deliveryDate")).toLocalDate();
                        Long count = ((Number) map.get("recordCount")).longValue();
                        return new DeliveryDateStatDTO(date, count);
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("[getDeliveryDateStatistics][统计查询异常]", e);
            return new ArrayList<>();
        }
    }

    /**
     * 执行原始查询方式的OMS明细结算
     * 
     * @param billNos 单据编号列表
     * @param shopNos 店铺编号列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    private void executeOriginalOMSDetailSettle(List<String> billNos, List<String> shopNos, LocalDate startDate, LocalDate endDate) {
        // 根据单据编号查询结算状态为"2"的发货明细
        List<CqDeliveryDetailRespVO> cqDeliveryDetailRespVOs = cqDeliveryDetailService.getDeliveryDetailsByBillNo(billNos, "2", shopNos, startDate, endDate);
        if (CollectionUtils.isEmpty(cqDeliveryDetailRespVOs)) {
            log.info("[executeOriginalOMSDetailSettle][查询条件: billNos={}, shopNos={}, startDate={}, endDate={} 没有可结算的发货明细]", 
                    billNos != null ? billNos.size() : 0, 
                    shopNos != null ? shopNos.size() : 0, startDate, endDate);
            return;
        }

        // 加载缓存数据
        OMSSettleCacheData cacheData = loadOMSSettleCacheData();
        
        // 批量处理发货明细
        processBatchOMSDeliveryDetails(cqDeliveryDetailRespVOs, cacheData, "executeOriginalOMSDetailSettle");
    }

    /**
     * 执行原始查询方式的主单结算
     * 
     * @param billNos 单据编号列表
     * @param shopNos 店铺编号列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    private void executeOriginalMainSettle(List<String> billNos, List<String> shopNos, LocalDate startDate, LocalDate endDate) {
        // 根据单据编号查询结算状态为待结算或结算异常的发货明细
        List<CqDeliveryDetailRespVO> cqDeliveryDetailRespVOs = cqDeliveryDetailService.getDeliveryDetailsByBillNo(billNos, "1", shopNos, startDate, endDate);
        if (CollectionUtils.isEmpty(cqDeliveryDetailRespVOs)) {
            log.info("[executeOriginalMainSettle][查询条件: billNos={}, shopNos={}, startDate={}, endDate={} 没有可结算的发货明细]", 
                    billNos != null ? billNos.size() : 0, 
                    shopNos != null ? shopNos.size() : 0, startDate, endDate);
            return;
        }

        // 加载缓存数据
        MainSettleCacheData cacheData = loadMainSettleCacheData();
        
        // 批量处理发货明细
        processBatchDeliveryDetails(cqDeliveryDetailRespVOs, cacheData, "executeOriginalMainSettle");
    }

    /**
     * 执行分层查询方式的OMS明细结算
     * 
     * @param dateStats 日期统计信息列表
     * @param billNos 单据编号列表
     * @param shopNos 店铺编号列表
     * @param methodStartTime 方法开始时间戳
     */
    private void executeLayeredOMSDetailSettle(List<DeliveryDateStatDTO> dateStats, List<String> billNos, List<String> shopNos, long methodStartTime) {
        int totalDates = dateStats.size();
        SettleStatistics totalStatistics = new SettleStatistics();
        
        // 加载缓存数据（一次性获取，避免重复查询）
        OMSSettleCacheData cacheData = loadOMSSettleCacheData();
        
        log.info("[executeLayeredOMSDetailSettle][开始分层处理，共{}个日期]", totalDates);
        
        // 按日期降序处理
        for (int i = 0; i < dateStats.size(); i++) {
            DeliveryDateStatDTO dateStat = dateStats.get(i);
            LocalDate currentDate = dateStat.getDeliveryDate();
            Long recordCount = dateStat.getRecordCount();
            
            try {
                // 记录单日处理开始
                long dayStartTime = System.currentTimeMillis();
                
                // 检查单日记录数是否过多
                if (recordCount > MAX_DAILY_RECORDS_THRESHOLD) {
                    log.warn("[executeLayeredOMSDetailSettle][日期 {} 的记录数({})超过单日阈值({})]", 
                            currentDate, recordCount, MAX_DAILY_RECORDS_THRESHOLD);
                }
                
                // 查询当日数据（传入单天的日期范围）
                List<CqDeliveryDetailRespVO> dayDeliveryDetails = cqDeliveryDetailService.getDeliveryDetailsByBillNo(
                        billNos, "2", shopNos, currentDate, currentDate);
                
                // 处理当日的发货明细
                SettleStatistics dayStatistics = processBatchOMSDeliveryDetails(dayDeliveryDetails, cacheData,
                        "executeLayeredOMSDetailSettle-" + currentDate);
                
                // 合并统计信息
                totalStatistics.merge(dayStatistics);
                
                long dayEndTime = System.currentTimeMillis();
                
                // 记录进度
                logDateQueryProgress(currentDate, recordCount, totalStatistics.getTotalProcessed(), totalDates, 
                                   i + 1, dayEndTime - dayStartTime);
                
            } catch (Exception e) {
                log.error("[executeLayeredOMSDetailSettle][处理日期 {} 时发生异常，跳过该日期]", currentDate, e);
            }
        }
        
        totalStatistics.markEnd();
        long methodEndTime = System.currentTimeMillis();
        log.info("[executeLayeredOMSDetailSettle][分层OMS明细结算完成，总耗时: {}ms, {}]", 
                methodEndTime - methodStartTime, totalStatistics.getSummary());
    }

    /**
     * 执行分层查询方式的主单结算
     * 
     * @param dateStats 日期统计信息列表
     * @param billNos 单据编号列表
     * @param shopNos 店铺编号列表
     * @param methodStartTime 方法开始时间戳
     */
    private void executeLayeredMainSettle(List<DeliveryDateStatDTO> dateStats, List<String> billNos, List<String> shopNos, long methodStartTime) {
        int totalDates = dateStats.size();
        SettleStatistics totalStatistics = new SettleStatistics();
        
        // 加载缓存数据（一次性获取，避免重复查询）
        MainSettleCacheData cacheData = loadMainSettleCacheData();
        
        log.info("[executeLayeredMainSettle][开始分层处理，共{}个日期]", totalDates);
        
        // 按日期降序处理
        for (int i = 0; i < dateStats.size(); i++) {
            DeliveryDateStatDTO dateStat = dateStats.get(i);
            LocalDate currentDate = dateStat.getDeliveryDate();
            Long recordCount = dateStat.getRecordCount();
            
            try {
                // 记录单日处理开始
                long dayStartTime = System.currentTimeMillis();
                
                // 检查单日记录数是否过多
                if (recordCount > MAX_DAILY_RECORDS_THRESHOLD) {
                    log.warn("[executeLayeredMainSettle][日期 {} 的记录数({})超过单日阈值({})]", 
                            currentDate, recordCount, MAX_DAILY_RECORDS_THRESHOLD);
                }
                
                // 查询当日数据（传入单天的日期范围）
                List<CqDeliveryDetailRespVO> dayDeliveryDetails = cqDeliveryDetailService.getDeliveryDetailsByBillNo(
                        billNos, "1", shopNos, currentDate, currentDate);
                
                // 处理当日的发货明细
                SettleStatistics dayStatistics = processBatchDeliveryDetails(dayDeliveryDetails, cacheData,
                        "executeLayeredMainSettle-" + currentDate);
                
                // 合并统计信息
                totalStatistics.merge(dayStatistics);
                
                long dayEndTime = System.currentTimeMillis();
                
                // 记录进度
                logDateQueryProgress(currentDate, recordCount, totalStatistics.getTotalProcessed(), totalDates, 
                                   i + 1, dayEndTime - dayStartTime);
                
            } catch (Exception e) {
                log.error("[executeLayeredMainSettle][处理日期 {} 时发生异常，跳过该日期]", currentDate, e);
            }
        }
        
        totalStatistics.markEnd();
        long methodEndTime = System.currentTimeMillis();
        log.info("[executeLayeredMainSettle][分层主单结算完成，总耗时: {}ms, {}]", 
                methodEndTime - methodStartTime, totalStatistics.getSummary());
    }

    /**
     * 记录分日期查询的进度日志
     * 
     * @param currentDate 当前处理日期
     * @param recordCount 当日记录数
     * @param totalProcessed 累计处理记录数
     * @param totalDates 总日期数
     * @param currentIndex 当前日期索引
     * @param dayDuration 当日处理耗时
     */
    private void logDateQueryProgress(LocalDate currentDate, Long recordCount, long totalProcessed, 
                                     int totalDates, int currentIndex, long dayDuration) {
        double progressPercent = (double) currentIndex / totalDates * 100;
        
        log.info("[doMainSettle][进度: {}/{} ({:.1f}%), 日期: {}, 当日记录数: {}, 当日耗时: {}ms, 累计处理: {}]", 
                currentIndex, totalDates, progressPercent, currentDate, recordCount, dayDuration, totalProcessed);
    }

    /**
     * 执行原始查询方式的拆单明细结算
     * 
     * @param billNos 单据编号列表
     * @param shopNos 店铺编号列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    private void executeOriginalSplitDetailSettle(List<String> billNos, List<String> shopNos, LocalDate startDate, LocalDate endDate) {
        // 根据单据编号查询结算状态为"3"的发货明细
        List<CqDeliveryDetailRespVO> cqDeliveryDetailRespVOs = cqDeliveryDetailService.getDeliveryDetailsByBillNo(billNos, "3", shopNos, startDate, endDate);
        if (CollectionUtils.isEmpty(cqDeliveryDetailRespVOs)) {
            log.info("[executeOriginalSplitDetailSettle][查询条件: billNos={}, shopNos={}, startDate={}, endDate={} 没有可结算的发货明细]", 
                    billNos != null ? billNos.size() : 0, 
                    shopNos != null ? shopNos.size() : 0, startDate, endDate);
            return;
        }

        // 加载缓存数据
        SplitSettleCacheData cacheData = loadSplitSettleCacheData();
        
        // 批量处理发货明细
        processBatchSplitDetailSettle(cqDeliveryDetailRespVOs, cacheData, "executeOriginalSplitDetailSettle");
    }

    /**
     * 批量处理拆单明细结算列表
     * 
     * @param deliveryDetails 发货明细列表
     * @param cacheData 拆单结算缓存数据
     * @param methodName 调用方法名（用于日志记录）
     * @return 处理统计信息
     */
    private SettleStatistics processBatchSplitDetailSettle(List<CqDeliveryDetailRespVO> deliveryDetails,
                                                           SplitSettleCacheData cacheData,
                                                           String methodName) {
        SettleStatistics statistics = new SettleStatistics();
        
        if (CollectionUtils.isEmpty(deliveryDetails)) {
            log.info("[{}][没有可结算的发货明细]", methodName);
            statistics.markEnd();
            return statistics;
        }
        
        log.info("[{}][开始处理拆单明细结算，共{}条发货明细]", methodName, deliveryDetails.size());
        
        // 遍历发货明细，进行结算
        for (CqDeliveryDetailRespVO deliveryDetail : deliveryDetails) {
            processSingleSplitDetailSettle(deliveryDetail, cacheData, statistics);
        }
        
        statistics.markEnd();
        log.info("[{}][拆单明细结算完成，{}]", methodName, statistics.getSummary());
        
        return statistics;
    }

    /**
     * 处理单条发货明细的拆单结算
     * 
     * @param deliveryDetail 发货明细
     * @param cacheData 拆单结算缓存数据
     * @param statistics 统计信息
     * @return 处理是否成功
     */
    private boolean processSingleSplitDetailSettle(CqDeliveryDetailRespVO deliveryDetail, 
                                                 SplitSettleCacheData cacheData, 
                                                 SettleStatistics statistics) {
        // 精确定位失败原因
        List<String> reasons = new ArrayList<>();
        try {
            // 1. 验证发货明细是否满足拆单条件
            if (!validateDeliveryDetail(deliveryDetail, reasons)) {
                cqDeliveryDetailService.updateSettleStatusByBillNo(deliveryDetail.getBillNo(), "3", null, String.join("、", reasons));
                statistics.incrementFail();
                return false;
            }

            // 2. 初始化拆单上下文信息
            OMSDetailSettleContext context = initializeSplitContextV2(deliveryDetail, cacheData);
            context.setBillNo(deliveryDetail.getBillNo()); // 设置单据编号用于日志记录

            // 3. 处理拆单明细分录
            List<CqSourceDeliveryDetailEntryDO> splitDetailEntries = new ArrayList<>();
            AtomicLong seqCounter = new AtomicLong(1); // 序号计数器

            // 4. 遍历处理每个明细
            for (CqDeliveryDetailEntryRespVO entry : deliveryDetail.getEntries()) {
                // 跳过剔除物料
                if ("1".equals(entry.getExcludeMaterial())) {
                    continue;
                }

                // 根据是否为组装品采用不同的处理逻辑
                if (entry.getIsAssembly() != null && entry.getIsAssembly() == 1) {
                    // 处理组装品
                    List<CqSourceDeliveryDetailEntryDO> assemblyEntries = 
                        processAssemblyItemV2(context, entry, seqCounter, cacheData);
                    splitDetailEntries.addAll(assemblyEntries);
                } else {
                    // 处理非组装品
                    CqSourceDeliveryDetailEntryDO nonAssemblyEntry = 
                        processNonAssemblyItemV2(context, entry, seqCounter.getAndIncrement(), cacheData);
                    splitDetailEntries.add(nonAssemblyEntry);
                }
            }

            // 5. 保存拆单明细分录并更新结算状态
            saveAndUpdateSettleStatus(deliveryDetail.getId(), deliveryDetail.getBillNo(), splitDetailEntries);
            statistics.incrementSuccess();
            return true;
            
        } catch (Exception e) {
            statistics.incrementFail();
            log.error("[processSingleSplitDetailSettle][单据编号: {} 精确定位失败]", deliveryDetail.getBillNo(), e);
            
            // 发送钉钉异常通知
            sendExceptionNotification(deliveryDetail.getBillNo(), e, "processSingleSplitDetailSettle");
            
            reasons.add(e.toString());
            // 更新结算状态为异常(3)，并记录失败原因
            cqDeliveryDetailService.updateSettleStatusByBillNo(deliveryDetail.getBillNo(), "3", null, String.join("、", reasons));
            return false;
        }
     }

    /**
     * 执行分层查询方式的拆单明细结算
     * 
     * @param dateStats 日期统计信息列表
     * @param billNos 单据编号列表
     * @param shopNos 店铺编号列表
     * @param methodStartTime 方法开始时间戳
     */
    private void executeLayeredSplitDetailSettle(List<DeliveryDateStatDTO> dateStats, List<String> billNos, List<String> shopNos, long methodStartTime) {
        int totalDates = dateStats.size();
        SettleStatistics totalStatistics = new SettleStatistics();
        
        // 加载缓存数据（一次性获取，避免重复查询）
        SplitSettleCacheData cacheData = loadSplitSettleCacheData();
        
        log.info("[executeLayeredSplitDetailSettle][开始分层处理，共{}个日期]", totalDates);
        
        // 按日期降序处理
        for (int i = 0; i < dateStats.size(); i++) {
            DeliveryDateStatDTO dateStat = dateStats.get(i);
            LocalDate currentDate = dateStat.getDeliveryDate();
            Long recordCount = dateStat.getRecordCount();
            
            try {
                // 记录单日处理开始
                long dayStartTime = System.currentTimeMillis();
                
                // 检查单日记录数是否过多
                if (recordCount > MAX_DAILY_RECORDS_THRESHOLD) {
                    log.warn("[executeLayeredSplitDetailSettle][日期 {} 的记录数({})超过单日阈值({})]", 
                            currentDate, recordCount, MAX_DAILY_RECORDS_THRESHOLD);
                }
                
                // 查询当日数据（传入单天的日期范围）
                List<CqDeliveryDetailRespVO> dayDeliveryDetails = cqDeliveryDetailService.getDeliveryDetailsByBillNo(
                        billNos, "3", shopNos, currentDate, currentDate);
                
                // 处理当日的发货明细
                SettleStatistics dayStatistics = processBatchSplitDetailSettle(dayDeliveryDetails, cacheData,
                        "executeLayeredSplitDetailSettle-" + currentDate);
                
                // 合并统计信息
                totalStatistics.merge(dayStatistics);
                
                long dayEndTime = System.currentTimeMillis();
                
                // 记录进度
                logDateQueryProgress(currentDate, recordCount, totalStatistics.getTotalProcessed(), totalDates, 
                                   i + 1, dayEndTime - dayStartTime);
                
            } catch (Exception e) {
                log.error("[executeLayeredSplitDetailSettle][处理日期 {} 时发生异常，跳过该日期]", currentDate, e);
            }
        }
        
        totalStatistics.markEnd();
        long methodEndTime = System.currentTimeMillis();
        log.info("[executeLayeredSplitDetailSettle][分层拆单明细结算完成，总耗时: {}ms, {}]", 
                methodEndTime - methodStartTime, totalStatistics.getSummary());
    }

    /**
     * 解析结算任务参数
     * 
     * @param param JSON格式的参数字符串，支持格式：{"billNos": ["单号1", "单号2", ...], "shopNos": ["店铺1", "店铺2", ...], "startDate": "2024-01-01", "endDate": "2024-01-31"}
     * @return 解析后的参数对象
     */
    @Override
    public DeliveryDetailSettleParamsDTO parseSettleParams(String param) {
        DeliveryDetailSettleParamsDTO paramsDTO = new DeliveryDetailSettleParamsDTO();
        
        if (StrUtil.isNotBlank(param)) {
            try {
                // 解析JSON参数
                JSONObject jsonParam = JSONUtil.parseObj(param);
                
                // 解析并过滤billNos中的空字符串
                List<String> rawBillNos = jsonParam.getBeanList("billNos", String.class);
                if (rawBillNos != null) {
                    List<String> validBillNos = EnhancedCollectionUtils.filterValidStringElements(rawBillNos);
                    paramsDTO.setBillNos(validBillNos.isEmpty() ? null : validBillNos);
                }
                
                // 解析并过滤shopNos中的空字符串
                List<String> rawShopNos = jsonParam.getBeanList("shopNos", String.class);
                if (rawShopNos != null) {
                    List<String> validShopNos = EnhancedCollectionUtils.filterValidStringElements(rawShopNos);
                    paramsDTO.setShopNos(validShopNos.isEmpty() ? null : validShopNos);
                }
                
                // 解析日期参数
                String startDateStr = jsonParam.getStr("startDate");
                String endDateStr = jsonParam.getStr("endDate");
                if (StrUtil.isNotBlank(startDateStr)) {
                    paramsDTO.setStartDate(LocalDate.parse(startDateStr));
                }
                if (StrUtil.isNotBlank(endDateStr)) {
                    paramsDTO.setEndDate(LocalDate.parse(endDateStr));
                }
                
                log.info("[parseSettleParams][参数解析成功: billNos={}, shopNos={}, startDate={}, endDate={}]", 
                        paramsDTO.getBillNos() != null ? paramsDTO.getBillNos().size() : 0, 
                        paramsDTO.getShopNos() != null ? paramsDTO.getShopNos().size() : 0,
                        paramsDTO.getStartDate(), paramsDTO.getEndDate());
            } catch (Exception e) {
                log.warn("[parseSettleParams][解析参数失败: {}]", param, e);
            }
        } else {
            log.info("[parseSettleParams][参数为空: {}]", param);
        }
        
        return paramsDTO;
    }

    /**
     * 发送异常通知到钉钉群
     * 
     * 该方法用于在发货明细结算过程中发生异常时，向钉钉群发送异常通知。
     * 通知内容包括单据编号、异常类型、异常信息、发生时间等关键信息。
     * 
     * @param billNo 单据编号，用于标识出现异常的具体单据
     * @param exception 异常对象，包含异常类型和异常信息
     * @param methodName 发生异常的方法名称，用于定位异常发生位置
     */
    private void sendExceptionNotification(String billNo, Exception exception, String methodName) {
        try {
            // 格式化当前时间
            String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            
            // 获取异常信息
            String exceptionType = exception.getClass().getSimpleName();
            String exceptionMessage = exception.getMessage() != null ? exception.getMessage() : "无异常信息";
            
            // 获取简化的堆栈跟踪信息（只取前5行关键信息）
            String stackTrace = getSimplifiedStackTrace(exception);
            
            // 构建Markdown格式的异常通知消息
            String title = "❌ 发货明细结算异常通知";
            String content = String.format(
                "## ❌ 发货明细结算异常通知\n\n" +
                "**发生时间**: %s\n\n" +
                "**单据编号**: `%s`\n\n" +
                "**异常方法**: `%s`\n\n" +
                "**异常类型**: `%s`\n\n" +
                "**异常信息**: %s\n\n" +
                "**堆栈跟踪**:\n\n```java\n%s\n```\n\n" +
                "---\n\n" +
                "**处理建议**: 请及时检查该单据的结算状态，排查异常原因并进行相应处理。\n\n" +
                "*此消息由发货明细结算系统自动发送*",
                currentTime,
                billNo,
                methodName,
                exceptionType,
                exceptionMessage,
                stackTrace
            );
            
            // 发送钉钉消息到异常通知机器人
            boolean success = dingDingUtils.sendMarkdownMessage(title, content, "exception-notify");
            
            if (success) {
                log.info("[sendExceptionNotification][钉钉异常通知发送成功][单据编号: {}][方法: {}]", billNo, methodName);
            } else {
                log.warn("[sendExceptionNotification][钉钉异常通知发送失败][单据编号: {}][方法: {}]", billNo, methodName);
            }
            
        } catch (Exception e) {
            // 确保钉钉通知异常不影响主业务流程
            log.warn("[sendExceptionNotification][发送钉钉异常通知时发生异常][单据编号: {}][方法: {}][异常: {}]", 
                    billNo, methodName, e.getMessage(), e);
        }
    }
    
    /**
     * 获取简化的堆栈跟踪信息
     * 
     * 为了避免钉钉消息过长，只提取关键的堆栈跟踪信息（前5行）
     * 
     * @param exception 异常对象
     * @return 简化的堆栈跟踪字符串
     */
    private String getSimplifiedStackTrace(Exception exception) {
        StackTraceElement[] stackTrace = exception.getStackTrace();
        if (stackTrace == null || stackTrace.length == 0) {
            return "无堆栈跟踪信息";
        }
        
        StringBuilder sb = new StringBuilder();
        int maxLines = Math.min(5, stackTrace.length); // 最多显示5行
        
        for (int i = 0; i < maxLines; i++) {
            StackTraceElement element = stackTrace[i];
            sb.append("at ").append(element.toString()).append("\n");
        }
        
        if (stackTrace.length > maxLines) {
            sb.append("... 还有 ").append(stackTrace.length - maxLines).append(" 行");
        }
        
        return sb.toString();
    }

}