package cn.iocoder.yudao.module.sap.service.cangqiong.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.CqBomWithEntriesDTO;
import cn.iocoder.yudao.module.sap.dal.dataobject.bom.CqBomDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.bom.CqBomEntryDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.bom.CqBomLangDO;
import cn.iocoder.yudao.module.sap.dal.mysql.bom.CqBomMapper;
import cn.iocoder.yudao.module.sap.dal.mysql.bom.CqBomEntryMapper;
import cn.iocoder.yudao.module.sap.dal.mysql.bom.CqBomLangMapper;
import cn.iocoder.yudao.module.sap.model.bom.BomCustomerKey;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqBomService;
import lombok.extern.slf4j.Slf4j;

/**
 * 销售BOM Service 实现类
 */
@Service
@Slf4j
@DS("cq_scm")
public class CqBomServiceImpl implements CqBomService {

    /**
     * 分批查询的批次大小，避免IN子句参数过多导致数据库异常
     */
    private static final int BATCH_SIZE = 500;

    @Autowired
    private CqBomMapper cqBomMapper;
    
    @Autowired
    private CqBomEntryMapper cqBomEntryMapper;
    
    @Autowired
    private CqBomLangMapper cqBomLangMapper;
    
    @Override
    public CqBomDO getCqBom(Long id) {
        return cqBomMapper.selectById(id);
    }
    
    @Override
    public CqBomDO getCqBomByNumber(String number) {
        return cqBomMapper.selectByNumber(number);
    }
    
    @Override
    public CqBomDO getCqBomByBomNo(String bomNo) {
        return cqBomMapper.selectByBomNo(bomNo);
    }
    
    @Override
    public List<CqBomDO> getCqBomsByStatus(String status) {
        return cqBomMapper.selectByStatus(status);
    }
    
    @Override
    public List<CqBomEntryDO> getCqBomEntries(Long bomId) {
        return cqBomEntryMapper.selectByBomId(bomId);
    }
    
    @Override
    public List<CqBomLangDO> getCqBomLangs(Long bomId) {
        return cqBomLangMapper.selectByBomId(bomId);
    }
    
    @Override
    public List<CqBomEntryDO> getBomStructureByBomNo(String bomNo) {
        // 先根据主商品编码查询主表信息
        CqBomDO bomDO = getCqBomByBomNo(bomNo);
        if (bomDO == null) {
            log.info("未找到主商品编码对应的BOM信息, bomNo: {}", bomNo);
            return new ArrayList<>();
        }
        
        // 再查询子表信息
        return getCqBomEntries(bomDO.getId());
    }
    
    @Override
    public List<CqBomDO> getActiveBomsByCustomer(Long customerId) {
        // 查询指定客户的BOM，并过滤启用状态
        List<CqBomDO> customerBoms = cqBomMapper.selectByCustomerId(customerId);
        if (CollectionUtils.isEmpty(customerBoms)) {
            return new ArrayList<>();
        }
        
        // 过滤启用状态的BOM
        return customerBoms.stream()
                .filter(bom -> "1".equals(bom.getEnable()))
                .collect(Collectors.toList());
    }
    
    @Override
    public CqBomDO getBomDetailWithEntries(Long bomId) {
        // 查询主表信息
        CqBomDO bomDO = getCqBom(bomId);
        if (bomDO == null) {
            log.info("未找到BOM主表信息, bomId: {}", bomId);
            return null;
        }
        
        // 查询子表信息（这里仅返回主表信息，子表信息可通过单独方法获取）
        return bomDO;
    }
    
    @Override
    public List<CqBomDO> getAllBomStructures() {
        // 查询所有审核状态的BOM
        return getCqBomsByStatus("C");
    }
    
    @Override
    public boolean isBomNoExists(String bomNo) {
        CqBomDO bomDO = getCqBomByBomNo(bomNo);
        return bomDO != null;
    }
    
    /**
     * 批量查询BOM子表数据
     * 
     * 采用分批查询策略，避免IN子句参数过多导致数据库异常。
     * 支持的数据库IN子句限制：
     * - Oracle: 最多1000个参数
     * - MySQL: 理论上无限制，但实际受max_allowed_packet限制
     * - PostgreSQL: 理论上无限制，但性能会随参数增加而下降
     * - SQL Server: 最多2100个参数
     * 
     * @param bomIds BOM主表ID列表
     * @return 按BOM ID分组的子表数据映射
     */
    private Map<Long, List<CqBomEntryDO>> batchQueryBomEntriesByBomIds(List<Long> bomIds) {
        if (CollectionUtils.isEmpty(bomIds)) {
            log.debug("[batchQueryBomEntriesByBomIds][输入的BOM ID列表为空]");
            return new HashMap<>();
        }
        
        long startTime = System.currentTimeMillis();
        int totalBomIds = bomIds.size();
        int batchCount = (totalBomIds + BATCH_SIZE - 1) / BATCH_SIZE;
        
        log.info("[batchQueryBomEntriesByBomIds][开始分批查询BOM子表数据，总数量: {}, 批次大小: {}, 预计批次数: {}]", 
                totalBomIds, BATCH_SIZE, batchCount);
        
        Map<Long, List<CqBomEntryDO>> resultMap = new HashMap<>();
        int processedCount = 0;
        
        try {
            // 分批处理，避免IN子句参数过多
            for (int i = 0; i < bomIds.size(); i += BATCH_SIZE) {
                int endIndex = Math.min(i + BATCH_SIZE, bomIds.size());
                List<Long> batchBomIds = bomIds.subList(i, endIndex);
                int currentBatchNumber = (i / BATCH_SIZE) + 1;
                
                log.debug("[batchQueryBomEntriesByBomIds][处理第{}批，批次大小: {}]", currentBatchNumber, batchBomIds.size());
                
                try {
                    // 批量查询当前批次的BOM子表数据
                    List<CqBomEntryDO> batchEntries = cqBomEntryMapper.selectList(
                            new LambdaQueryWrapper<CqBomEntryDO>()
                                    .in(CqBomEntryDO::getId, batchBomIds)
                    );
                    
                    // 按BOM ID分组并合并到结果集
                    Map<Long, List<CqBomEntryDO>> batchResultMap = batchEntries.stream()
                            .collect(Collectors.groupingBy(CqBomEntryDO::getId));
                    
                    resultMap.putAll(batchResultMap);
                    processedCount += batchBomIds.size();
                    
                    log.debug("[batchQueryBomEntriesByBomIds][第{}批处理完成，查询到{}条子表记录，累计处理: {}/{}]", 
                            currentBatchNumber, batchEntries.size(), processedCount, totalBomIds);
                    
                } catch (Exception e) {
                    log.error("[batchQueryBomEntriesByBomIds][第{}批查询失败，批次大小: {}]", currentBatchNumber, batchBomIds.size(), e);
                    // 继续处理后续批次，但记录错误
                }
            }
            
            long endTime = System.currentTimeMillis();
            long totalTime = endTime - startTime;
            
            log.info("[batchQueryBomEntriesByBomIds][分批查询完成，总耗时: {}ms, 处理BOM数量: {}, 返回分组数量: {}]", 
                    totalTime, processedCount, resultMap.size());
            
        } catch (Exception e) {
            log.error("[batchQueryBomEntriesByBomIds][分批查询过程中发生异常]", e);
            // 发生异常时返回已处理的部分结果
        }
        
        return resultMap;
    }
    
    /**
     * 获取所有已审核状态的销售BOM数据结构映射
     * 
     * 该方法返回一个Map结构，其中key为BOM主商品编码（bomNo字段），value为包含对应BOM主表信息和BOM子表信息的组合对象。
     * 
     * 返回值结构：
     * - Map的key：BOM主商品编码（CqBomDO.bomNo字段）
     * - Map的value：CqBomWithEntriesDTO对象，包含：
     *   - bomInfo：BOM主表信息（CqBomDO对象）
     *   - entries：BOM子表信息列表（List<CqBomEntryDO>对象）
     * 
     * 性能考虑：
     * - 使用批量查询策略，减少数据库访问次数
     * - 适合一次性获取所有已审核BOM数据的场景
     * - 对于大量数据，建议考虑分页或缓存机制
     * 
     * @return 已审核BOM数据结构映射，key为BOM主商品编码，value为BOM主表和子表的组合对象
     */
    @Override
    public Map<String, CqBomWithEntriesDTO> getAllAuditedBomStructuresMap() {
        log.info("[getAllAuditedBomStructuresMap][开始获取所有已审核BOM数据结构映射]");
        
        // 1. 获取所有已审核状态的BOM主表数据
        List<CqBomDO> auditedBoms = getCqBomsByStatus("C");
        if (CollectionUtils.isEmpty(auditedBoms)) {
            log.info("[getAllAuditedBomStructuresMap][未找到已审核状态的BOM数据]");
            return new HashMap<>();
        }
        
        log.info("[getAllAuditedBomStructuresMap][找到已审核BOM数量: {}]", auditedBoms.size());
        
        // 2. 提取所有BOM主表的ID列表
        List<Long> bomIds = auditedBoms.stream()
                .map(CqBomDO::getId)
                .collect(Collectors.toList());
        
        // 3. 批量查询所有相关的BOM子表数据
        Map<Long, List<CqBomEntryDO>> bomIdToEntriesMap = batchQueryBomEntriesByBomIds(bomIds);
        
        // 4. 构建返回的Map结构
        Map<String, CqBomWithEntriesDTO> resultMap = new HashMap<>();
        
        for (CqBomDO bomDO : auditedBoms) {
            String bomNumber = bomDO.getBomNo();
            if (bomNumber == null) {
                log.warn("[getAllAuditedBomStructuresMap][BOM主商品编码为空，跳过处理, bomId: {}]", bomDO.getId());
                continue;
            }
            
            // 获取对应的子表数据，如果没有则使用空列表
            List<CqBomEntryDO> entries = bomIdToEntriesMap.getOrDefault(bomDO.getId(), new ArrayList<>());
            
            // 构建组合DTO
            CqBomWithEntriesDTO bomWithEntries = CqBomWithEntriesDTO.build(bomDO, entries);
            resultMap.put(bomNumber, bomWithEntries);
        }
        
        log.info("[getAllAuditedBomStructuresMap][处理完成，返回BOM数据结构映射数量: {}]", resultMap.size());
        return resultMap;
    }
    
    /**
     * 获取所有已审核状态的销售BOM数据结构映射（按客户分组）
     * 
     * 该方法返回一个Map结构，其中key为BOM编码与客户ID的组合键，value为包含对应BOM主表信息和BOM子表信息的组合对象。
     * 相比于 getAllAuditedBomStructuresMap() 方法，本方法可以区分相同BOM编码但不同客户的数据。
     * 
     * 返回值结构：
     * - Map的key：BomCustomerKey对象，包含bomNo和customerId
     * - Map的value：CqBomWithEntriesDTO对象，包含：
     *   - bomInfo：BOM主表信息（CqBomDO对象）
     *   - entries：BOM子表信息列表（List<CqBomEntryDO>对象）
     * 
     * 性能考虑：
     * - 使用批量查询策略，减少数据库访问次数
     * - 适合一次性获取所有已审核BOM数据的场景
     * - 对于大量数据，建议考虑分页或缓存机制
     * 
     * @return 已审核BOM数据结构映射，key为BOM编码与客户ID组合键，value为BOM主表和子表的组合对象
     */
    @Override
    public Map<BomCustomerKey, CqBomWithEntriesDTO> getAllAuditedBomStructuresMapByCustomer() {
        log.info("[getAllAuditedBomStructuresMapByCustomer][开始获取所有已审核BOM数据结构映射（按客户分组）]");
        
        // 1. 获取所有已审核状态的BOM主表数据
        List<CqBomDO> auditedBoms = getCqBomsByStatus("C");
        if (CollectionUtils.isEmpty(auditedBoms)) {
            log.info("[getAllAuditedBomStructuresMapByCustomer][未找到已审核状态的BOM数据]");
            return new HashMap<>();
        }
        
        log.info("[getAllAuditedBomStructuresMapByCustomer][找到已审核BOM数量: {}]", auditedBoms.size());
        
        // 2. 提取所有BOM主表的ID列表
        List<Long> bomIds = auditedBoms.stream()
                .map(CqBomDO::getId)
                .collect(Collectors.toList());
        
        // 3. 批量查询所有相关的BOM子表数据
        Map<Long, List<CqBomEntryDO>> bomIdToEntriesMap = batchQueryBomEntriesByBomIds(bomIds);
        
        // 4. 构建返回的Map结构
        Map<BomCustomerKey, CqBomWithEntriesDTO> resultMap = new HashMap<>();
        
        for (CqBomDO bomDO : auditedBoms) {
            String bomNumber = bomDO.getBomNo();
            Long customerId = bomDO.getCustomerId();
            
            if (bomNumber == null) {
                log.warn("[getAllAuditedBomStructuresMapByCustomer][BOM主商品编码为空，跳过处理, bomId: {}]", bomDO.getId());
                continue;
            }
            
            if (customerId == null) {
                log.warn("[getAllAuditedBomStructuresMapByCustomer][客户ID为空，跳过处理, bomId: {}, bomNo: {}]", bomDO.getId(), bomNumber);
                continue;
            }
            
            // 创建组合键
            BomCustomerKey bomCustomerKey = new BomCustomerKey(bomNumber, customerId);
            
            // 获取对应的子表数据，如果没有则使用空列表
            List<CqBomEntryDO> entries = bomIdToEntriesMap.getOrDefault(bomDO.getId(), new ArrayList<>());
            
            // 构建组合DTO
            CqBomWithEntriesDTO bomWithEntries = CqBomWithEntriesDTO.build(bomDO, entries);
            resultMap.put(bomCustomerKey, bomWithEntries);
        }
        
        log.info("[getAllAuditedBomStructuresMapByCustomer][处理完成，返回BOM数据结构映射数量: {}]", resultMap.size());
        return resultMap;
    }
} 