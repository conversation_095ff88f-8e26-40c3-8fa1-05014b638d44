package cn.iocoder.yudao.module.sap.dal.mysql.e3;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.sap.dal.dataobject.e3.CqE3ChannelDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collections;
import java.util.List;

/**
 * E3渠道 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CqE3ChannelMapper extends BaseMapperX<CqE3ChannelDO> {

    /**
     * 根据渠道编号查询渠道信息
     *
     * @param number 渠道编号
     * @return 渠道信息
     */
    default CqE3ChannelDO selectByNumber(String number) {
        return selectOne(CqE3ChannelDO::getFnumber, number);
    }

    /**
     * 根据状态查询渠道列表
     *
     * @param status 状态
     * @return 渠道列表
     */
    default List<CqE3ChannelDO> selectByStatus(String status) {
        return selectList(CqE3ChannelDO::getFstatus, status);
    }

    /**
     * 根据区域查询渠道列表
     *
     * @param area 区域
     * @return 渠道列表
     */
    default List<CqE3ChannelDO> selectByArea(String area) {
        return selectList(CqE3ChannelDO::getFkYdArea, area);
    }

    /**
     * 根据上级渠道ID查询子渠道列表
     *
     * @param parentChannelId 上级渠道ID
     * @return 子渠道列表
     */
    default List<CqE3ChannelDO> selectByParentChannelId(Long parentChannelId) {
        return selectList(CqE3ChannelDO::getFkYdSuperChannelid, parentChannelId);
    }

    /**
     * 根据状态和使用状态查询渠道列表
     *
     * @param status 数据状态
     * @param enable 使用状态
     * @return 渠道列表
     */
    default List<CqE3ChannelDO> selectByStatusAndEnable(String status, String enable) {
        return selectList(new cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX<CqE3ChannelDO>()
                .eq(CqE3ChannelDO::getFstatus, status)
                .eq(CqE3ChannelDO::getFenable, enable));
    }

    /**
     * 根据渠道ID列表批量查询渠道信息
     *
     * @param channelIds 渠道ID列表
     * @return 渠道信息列表
     */
    default List<CqE3ChannelDO> selectByIds(List<Long> channelIds) {
        if (channelIds == null || channelIds.isEmpty()) {
            return Collections.emptyList();
        }
        return selectBatchIds(channelIds);
    }
} 