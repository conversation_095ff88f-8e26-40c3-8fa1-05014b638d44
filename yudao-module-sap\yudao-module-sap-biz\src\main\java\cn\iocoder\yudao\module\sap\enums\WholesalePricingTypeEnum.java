package cn.iocoder.yudao.module.sap.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 批发取价类型枚举
 * 
 * 用于替代批发取价表中的硬编码价格类型值，提供类型安全和语义明确的枚举定义。
 * 支持实际金额、标准金额和分销供货价三种取价方式。
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum WholesalePricingTypeEnum {

    /**
     * 实际金额
     * 取批发单据中的实际金额字段
     */
    ACTUAL_AMOUNT("ACTUAL_AMOUNT", "实际金额"),
    
    /**
     * 标准金额
     * 取批发单据中的标准金额字段
     */
    STANDARD_AMOUNT("STANDARD_AMOUNT", "标准金额"),
    
    /**
     * 分销供货价
     * 根据物料编码+组织编码+苍穹客户编码+业务日期/退货日期匹配经销商供货价格表
     */
    DISTRIBUTION_PRICE("DISTRIBUTION_PRICE", "分销供货价");

    /**
     * 枚举代码
     */
    private final String code;
    
    /**
     * 枚举名称
     */
    private final String name;
    
    /**
     * 根据代码获取枚举
     *
     * @param code 枚举代码
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static WholesalePricingTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (WholesalePricingTypeEnum pricingType : values()) {
            if (pricingType.getCode().equals(code)) {
                return pricingType;
            }
        }
        return null;
    }
    
    /**
     * 根据代码获取名称
     *
     * @param code 枚举代码
     * @return 对应的枚举名称，如果未找到则返回空字符串
     */
    public static String getNameByCode(String code) {
        WholesalePricingTypeEnum pricingType = getByCode(code);
        return pricingType != null ? pricingType.getName() : "";
    }
} 