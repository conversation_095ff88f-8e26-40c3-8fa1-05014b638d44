### 1. 完整参数测试 - 包含公司代码、订单号和商店编码
POST http://127.0.0.1:48001/admin-api/sap/e3-order-sync-test/sync-by-params
Content-Type: application/json

{
  "companyCode": "maiyou",
  "orderNo": "125031441609550",
  "storeCode": "temp-01-1"
}

### 2. 最小参数测试 - 仅包含必填参数
POST http://127.0.0.1:48001/admin-api/sap/e3-order-sync-test/sync-by-params
Content-Type: application/json

{
  "companyCode": "maiyou",
  "orderNo": "125031441609550"
}

### 3. 麦优公司测试 - 使用maiyou公司代码
POST http://127.0.0.1:48001/admin-api/sap/e3-order-sync-test/sync-by-params
Content-Type: application/json

{
  "companyCode": "maiyou",
  "orderNo": "125040845106169",
  "storeCode": "temp-01-1"
}

### 4. 佰悦公司测试 - 使用baiyue公司代码
POST http://127.0.0.1:48001/admin-api/sap/e3-order-sync-test/sync-by-params
Content-Type: application/json

{
  "companyCode": "baiyue",
  "orderNo": "125040845106169",
  "storeCode": "temp-01-1"
}

### 5. 特定订单测试 - 使用具体的订单号
POST http://127.0.0.1:48001/admin-api/sap/e3-order-sync-test/sync-by-params
Content-Type: application/json

{
  "companyCode": "baiyue",
  "orderNo": "225071081621504"
}

### 6. 麦优公司无商店编码测试
POST http://127.0.0.1:48001/admin-api/sap/e3-order-sync-test/sync-by-params
Content-Type: application/json

{
  "companyCode": "maiyou",
  "orderNo": "125030640094075"
}

### 7. 佰悦公司无商店编码测试
POST http://127.0.0.1:48001/admin-api/sap/e3-order-sync-test/sync-by-params
Content-Type: application/json

{
  "companyCode": "baiyue",
  "orderNo": "225062580254744"
}

### ===================================临时测试==========================================
### ===================================================================================

### 临时测试1 - 自定义测试用例
POST http://127.0.0.1:48001/admin-api/sap/e3-order-sync-test/sync-by-params
Content-Type: application/json

{
  "companyCode": "maiyou",
  "orderNo": "YOUR_ORDER_NO_HERE",
  "storeCode": "YOUR_STORE_CODE_HERE"
}

### 临时测试2 - 批量测试准备
POST http://127.0.0.1:48001/admin-api/sap/e3-order-sync-test/sync-by-params
Content-Type: application/json

{
  "companyCode": "baiyue",
  "orderNo": "225072962994014"
} 