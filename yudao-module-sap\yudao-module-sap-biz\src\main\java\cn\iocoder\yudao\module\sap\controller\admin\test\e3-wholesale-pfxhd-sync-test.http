### 1. 麦优公司日期范围同步测试 - 包含开始和结束日期
POST http://127.0.0.1:48001/admin-api/sap/e3-wholesale-pfxhd-sync-test/sync-by-params
Content-Type: application/json

{
  "companyCode": "maiyou",
  "startDate": "2024-01-01",
  "endDate": "2024-01-31"
}

### 2. 佰跃公司日期范围同步测试 - 包含开始和结束日期
POST http://127.0.0.1:48001/admin-api/sap/e3-wholesale-pfxhd-sync-test/sync-by-params
Content-Type: application/json

{
  "companyCode": "baiyue",
  "startDate": "2025-07-01",
  "endDate": "2025-07-31",
  "djbh": "PFXHD2025072400006"
}

### 3. 麦优公司单据编号同步测试 - 仅指定单据编号
POST http://127.0.0.1:48001/admin-api/sap/e3-wholesale-pfxhd-sync-test/sync-by-params
Content-Type: application/json

{
  "companyCode": "maiyou",
  "djbh": "PF202401010001"
}

### 4. 佰跃公司单据编号同步测试 - 仅指定单据编号
POST http://127.0.0.1:48001/admin-api/sap/e3-wholesale-pfxhd-sync-test/sync-by-params
Content-Type: application/json

{
  "companyCode": "baiyue",
  "djbh": "PF202401010002"
}

### 5. 最小参数测试 - 仅包含公司代码（使用默认日期范围）
POST http://127.0.0.1:48001/admin-api/sap/e3-wholesale-pfxhd-sync-test/sync-by-params
Content-Type: application/json

{
  "companyCode": "maiyou"
}

### 6. 完整参数测试 - 同时包含日期和单据编号（优先使用单据编号）
POST http://127.0.0.1:48001/admin-api/sap/e3-wholesale-pfxhd-sync-test/sync-by-params
Content-Type: application/json

{
  "companyCode": "maiyou",
  "startDate": "2024-01-01",
  "endDate": "2024-01-31",
  "djbh": "PF202401010003"
}

### 7. 麦优公司仅开始日期测试
POST http://127.0.0.1:48001/admin-api/sap/e3-wholesale-pfxhd-sync-test/sync-by-params
Content-Type: application/json

{
  "companyCode": "maiyou",
  "startDate": "2024-01-01"
}

### 8. 佰跃公司仅结束日期测试
POST http://127.0.0.1:48001/admin-api/sap/e3-wholesale-pfxhd-sync-test/sync-by-params
Content-Type: application/json

{
  "companyCode": "baiyue",
  "endDate": "2024-01-31"
}

### ===================================临时测试==========================================
### ===================================================================================

### 临时测试1 - 自定义测试用例
POST http://127.0.0.1:48001/admin-api/sap/e3-wholesale-pfxhd-sync-test/sync-by-params
Content-Type: application/json

{
  "companyCode": "maiyou",
  "startDate": "YOUR_START_DATE_HERE",
  "endDate": "YOUR_END_DATE_HERE"
}

### 临时测试2 - 自定义单据编号测试
POST http://127.0.0.1:48001/admin-api/sap/e3-wholesale-pfxhd-sync-test/sync-by-params
Content-Type: application/json

{
  "companyCode": "baiyue",
  "djbh": "PFXHD2025072900002"
} 